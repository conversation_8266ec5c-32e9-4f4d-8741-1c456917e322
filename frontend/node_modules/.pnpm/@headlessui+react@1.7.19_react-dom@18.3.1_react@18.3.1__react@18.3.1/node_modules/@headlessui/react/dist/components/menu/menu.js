import G,{createContext as X,createRef as N,Fragment as H,useContext as $,useEffect as q,useMemo as v,useReducer as z,useRef as K}from"react";import{useDisposables as j}from'../../hooks/use-disposables.js';import{useEvent as d}from'../../hooks/use-event.js';import{useId as O}from'../../hooks/use-id.js';import{useIsoMorphicEffect as L}from'../../hooks/use-iso-morphic-effect.js';import{useOutsideClick as Y}from'../../hooks/use-outside-click.js';import{useOwnerDocument as Z}from'../../hooks/use-owner.js';import{useResolveButtonType as ee}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as h}from'../../hooks/use-sync-refs.js';import{useTextValue as te}from'../../hooks/use-text-value.js';import{useTrackedPointer as ne}from'../../hooks/use-tracked-pointer.js';import{useTreeWalker as re}from'../../hooks/use-tree-walker.js';import{OpenClosedProvider as oe,State as D,useOpenClosed as ae}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as ie}from'../../utils/bugs.js';import{calculateActiveIndex as se,Focus as y}from'../../utils/calculate-active-index.js';import{disposables as k}from'../../utils/disposables.js';import{Focus as Q,FocusableMode as ue,focusFrom as le,isFocusableElement as pe,restoreFocusIfNecessary as W,sortByDomNode as ce}from'../../utils/focus-management.js';import{match as V}from'../../utils/match.js';import{Features as J,forwardRefWithAs as F,render as _}from'../../utils/render.js';import{Keys as c}from'../keyboard.js';var me=(r=>(r[r.Open=0]="Open",r[r.Closed=1]="Closed",r))(me||{}),de=(r=>(r[r.Pointer=0]="Pointer",r[r.Other=1]="Other",r))(de||{}),fe=(a=>(a[a.OpenMenu=0]="OpenMenu",a[a.CloseMenu=1]="CloseMenu",a[a.GoToItem=2]="GoToItem",a[a.Search=3]="Search",a[a.ClearSearch=4]="ClearSearch",a[a.RegisterItem=5]="RegisterItem",a[a.UnregisterItem=6]="UnregisterItem",a))(fe||{});function w(e,u=r=>r){let r=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,s=ce(u(e.items.slice()),t=>t.dataRef.current.domRef.current),i=r?s.indexOf(r):null;return i===-1&&(i=null),{items:s,activeItemIndex:i}}let Te={[1](e){return e.menuState===1?e:{...e,activeItemIndex:null,menuState:1}},[0](e){return e.menuState===0?e:{...e,__demoMode:!1,menuState:0}},[2]:(e,u)=>{var i;let r=w(e),s=se(u,{resolveItems:()=>r.items,resolveActiveIndex:()=>r.activeItemIndex,resolveId:t=>t.id,resolveDisabled:t=>t.dataRef.current.disabled});return{...e,...r,searchQuery:"",activeItemIndex:s,activationTrigger:(i=u.trigger)!=null?i:1}},[3]:(e,u)=>{let s=e.searchQuery!==""?0:1,i=e.searchQuery+u.value.toLowerCase(),o=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+s).concat(e.items.slice(0,e.activeItemIndex+s)):e.items).find(l=>{var m;return((m=l.dataRef.current.textValue)==null?void 0:m.startsWith(i))&&!l.dataRef.current.disabled}),a=o?e.items.indexOf(o):-1;return a===-1||a===e.activeItemIndex?{...e,searchQuery:i}:{...e,searchQuery:i,activeItemIndex:a,activationTrigger:1}},[4](e){return e.searchQuery===""?e:{...e,searchQuery:"",searchActiveItemIndex:null}},[5]:(e,u)=>{let r=w(e,s=>[...s,{id:u.id,dataRef:u.dataRef}]);return{...e,...r}},[6]:(e,u)=>{let r=w(e,s=>{let i=s.findIndex(t=>t.id===u.id);return i!==-1&&s.splice(i,1),s});return{...e,...r,activationTrigger:1}}},U=X(null);U.displayName="MenuContext";function C(e){let u=$(U);if(u===null){let r=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,C),r}return u}function ye(e,u){return V(u.type,Te,e,u)}let Ie=H;function Me(e,u){let{__demoMode:r=!1,...s}=e,i=z(ye,{__demoMode:r,menuState:r?0:1,buttonRef:N(),itemsRef:N(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:t,itemsRef:o,buttonRef:a},l]=i,m=h(u);Y([a,o],(g,R)=>{var p;l({type:1}),pe(R,ue.Loose)||(g.preventDefault(),(p=a.current)==null||p.focus())},t===0);let I=d(()=>{l({type:1})}),A=v(()=>({open:t===0,close:I}),[t,I]),f={ref:m};return G.createElement(U.Provider,{value:i},G.createElement(oe,{value:V(t,{[0]:D.Open,[1]:D.Closed})},_({ourProps:f,theirProps:s,slot:A,defaultTag:Ie,name:"Menu"})))}let ge="button";function Re(e,u){var R;let r=O(),{id:s=`headlessui-menu-button-${r}`,...i}=e,[t,o]=C("Menu.Button"),a=h(t.buttonRef,u),l=j(),m=d(p=>{switch(p.key){case c.Space:case c.Enter:case c.ArrowDown:p.preventDefault(),p.stopPropagation(),o({type:0}),l.nextFrame(()=>o({type:2,focus:y.First}));break;case c.ArrowUp:p.preventDefault(),p.stopPropagation(),o({type:0}),l.nextFrame(()=>o({type:2,focus:y.Last}));break}}),I=d(p=>{switch(p.key){case c.Space:p.preventDefault();break}}),A=d(p=>{if(ie(p.currentTarget))return p.preventDefault();e.disabled||(t.menuState===0?(o({type:1}),l.nextFrame(()=>{var M;return(M=t.buttonRef.current)==null?void 0:M.focus({preventScroll:!0})})):(p.preventDefault(),o({type:0})))}),f=v(()=>({open:t.menuState===0}),[t]),g={ref:a,id:s,type:ee(e,t.buttonRef),"aria-haspopup":"menu","aria-controls":(R=t.itemsRef.current)==null?void 0:R.id,"aria-expanded":t.menuState===0,onKeyDown:m,onKeyUp:I,onClick:A};return _({ourProps:g,theirProps:i,slot:f,defaultTag:ge,name:"Menu.Button"})}let Ae="div",be=J.RenderStrategy|J.Static;function Ee(e,u){var M,b;let r=O(),{id:s=`headlessui-menu-items-${r}`,...i}=e,[t,o]=C("Menu.Items"),a=h(t.itemsRef,u),l=Z(t.itemsRef),m=j(),I=ae(),A=(()=>I!==null?(I&D.Open)===D.Open:t.menuState===0)();q(()=>{let n=t.itemsRef.current;n&&t.menuState===0&&n!==(l==null?void 0:l.activeElement)&&n.focus({preventScroll:!0})},[t.menuState,t.itemsRef,l]),re({container:t.itemsRef.current,enabled:t.menuState===0,accept(n){return n.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:n.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(n){n.setAttribute("role","none")}});let f=d(n=>{var E,x;switch(m.dispose(),n.key){case c.Space:if(t.searchQuery!=="")return n.preventDefault(),n.stopPropagation(),o({type:3,value:n.key});case c.Enter:if(n.preventDefault(),n.stopPropagation(),o({type:1}),t.activeItemIndex!==null){let{dataRef:S}=t.items[t.activeItemIndex];(x=(E=S.current)==null?void 0:E.domRef.current)==null||x.click()}W(t.buttonRef.current);break;case c.ArrowDown:return n.preventDefault(),n.stopPropagation(),o({type:2,focus:y.Next});case c.ArrowUp:return n.preventDefault(),n.stopPropagation(),o({type:2,focus:y.Previous});case c.Home:case c.PageUp:return n.preventDefault(),n.stopPropagation(),o({type:2,focus:y.First});case c.End:case c.PageDown:return n.preventDefault(),n.stopPropagation(),o({type:2,focus:y.Last});case c.Escape:n.preventDefault(),n.stopPropagation(),o({type:1}),k().nextFrame(()=>{var S;return(S=t.buttonRef.current)==null?void 0:S.focus({preventScroll:!0})});break;case c.Tab:n.preventDefault(),n.stopPropagation(),o({type:1}),k().nextFrame(()=>{le(t.buttonRef.current,n.shiftKey?Q.Previous:Q.Next)});break;default:n.key.length===1&&(o({type:3,value:n.key}),m.setTimeout(()=>o({type:4}),350));break}}),g=d(n=>{switch(n.key){case c.Space:n.preventDefault();break}}),R=v(()=>({open:t.menuState===0}),[t]),p={"aria-activedescendant":t.activeItemIndex===null||(M=t.items[t.activeItemIndex])==null?void 0:M.id,"aria-labelledby":(b=t.buttonRef.current)==null?void 0:b.id,id:s,onKeyDown:f,onKeyUp:g,role:"menu",tabIndex:0,ref:a};return _({ourProps:p,theirProps:i,slot:R,defaultTag:Ae,features:be,visible:A,name:"Menu.Items"})}let Se=H;function xe(e,u){let r=O(),{id:s=`headlessui-menu-item-${r}`,disabled:i=!1,...t}=e,[o,a]=C("Menu.Item"),l=o.activeItemIndex!==null?o.items[o.activeItemIndex].id===s:!1,m=K(null),I=h(u,m);L(()=>{if(o.__demoMode||o.menuState!==0||!l||o.activationTrigger===0)return;let T=k();return T.requestAnimationFrame(()=>{var P,B;(B=(P=m.current)==null?void 0:P.scrollIntoView)==null||B.call(P,{block:"nearest"})}),T.dispose},[o.__demoMode,m,l,o.menuState,o.activationTrigger,o.activeItemIndex]);let A=te(m),f=K({disabled:i,domRef:m,get textValue(){return A()}});L(()=>{f.current.disabled=i},[f,i]),L(()=>(a({type:5,id:s,dataRef:f}),()=>a({type:6,id:s})),[f,s]);let g=d(()=>{a({type:1})}),R=d(T=>{if(i)return T.preventDefault();a({type:1}),W(o.buttonRef.current)}),p=d(()=>{if(i)return a({type:2,focus:y.Nothing});a({type:2,focus:y.Specific,id:s})}),M=ne(),b=d(T=>M.update(T)),n=d(T=>{M.wasMoved(T)&&(i||l||a({type:2,focus:y.Specific,id:s,trigger:0}))}),E=d(T=>{M.wasMoved(T)&&(i||l&&a({type:2,focus:y.Nothing}))}),x=v(()=>({active:l,disabled:i,close:g}),[l,i,g]);return _({ourProps:{id:s,ref:I,role:"menuitem",tabIndex:i===!0?void 0:-1,"aria-disabled":i===!0?!0:void 0,disabled:void 0,onClick:R,onFocus:p,onPointerEnter:b,onMouseEnter:b,onPointerMove:n,onMouseMove:n,onPointerLeave:E,onMouseLeave:E},theirProps:t,slot:x,defaultTag:Se,name:"Menu.Item"})}let Pe=F(Me),ve=F(Re),he=F(Ee),De=F(xe),qe=Object.assign(Pe,{Button:ve,Items:he,Item:De});export{qe as Menu};
