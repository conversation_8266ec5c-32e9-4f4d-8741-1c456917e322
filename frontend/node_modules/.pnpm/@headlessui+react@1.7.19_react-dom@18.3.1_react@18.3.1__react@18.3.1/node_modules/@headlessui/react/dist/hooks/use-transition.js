import{transition as f}from'../components/transitions/utils/transition.js';import{disposables as m}from'../utils/disposables.js';import{useDisposables as p}from'./use-disposables.js';import{useIsMounted as b}from'./use-is-mounted.js';import{useIsoMorphicEffect as o}from'./use-iso-morphic-effect.js';import{useLatestValue as g}from'./use-latest-value.js';function D({immediate:t,container:s,direction:n,classes:u,onStart:a,onStop:c}){let l=b(),d=p(),e=g(n);o(()=>{t&&(e.current="enter")},[t]),o(()=>{let r=m();d.add(r.dispose);let i=s.current;if(i&&e.current!=="idle"&&l.current)return r.dispose(),a.current(e.current),r.add(f(i,u.current,e.current==="enter",()=>{r.dispose(),c.current(e.current)})),r.dispose},[n])}export{D as useTransition};
