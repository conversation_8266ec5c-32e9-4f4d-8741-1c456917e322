{"name": "pvtsutils", "version": "1.3.6", "description": "pvtsutils is a set of common utility functions used in various Peculiar Ventures TypeScript based projects.", "main": "build/index.js", "module": "build/index.es.js", "browser": "build/index.js", "types": "build/index.d.ts", "files": ["build/**/*.{ts,js}", "README.md", "LICENSE"], "scripts": {"test": "mocha", "clear": "rimraf build/*", "rebuild": "npm run clear && npm run build", "build": "rollup -c", "lint": "tslint -p .", "lint:fix": "tslint --fix -p .", "prepub": "npm run lint && npm run rebuild", "pub": "npm version patch && npm publish", "postpub": "git push && git push --tags origin master", "prepub:next": "npm run lint && npm run rebuild", "pub:next": "npm version prerelease --preid=next && npm publish --tag next", "postpub:next": "git push", "coverage": "nyc npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["typescript", "helper", "util", "convert", "hex", "utf8", "utf16", "base64", "base64url", "binary", "assign"], "author": "PeculiarVentures", "contributors": ["<PERSON><PERSON><PERSON><<EMAIL>>"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/PeculiarVentures/pvtsutils"}, "bugs": {"url": "https://github.com/PeculiarVentures/pvtsutils/issues"}, "homepage": "https://github.com/PeculiarVentures/pvtsutils#readme", "dependencies": {"tslib": "^2.8.1"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^22.9.1", "mocha": "^10.8.2", "nyc": "^17.1.0", "rimraf": "^6.0.1", "rollup": "^4.27.3", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-typescript2": "^0.36.0", "ts-node": "^10.9.2", "tslint": "^6.1.3", "typescript": "^5.6.3"}, "resolutions": {"braces": "^3.0.3", "cross-spawn": "^7.0.6"}}