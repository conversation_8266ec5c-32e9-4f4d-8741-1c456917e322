# pvutils

[![test](https://github.com/PeculiarVentures/pvutils/actions/workflows/test.yml/badge.svg)](https://github.com/PeculiarVentures/pvutils/actions/workflows/test.yml)
[![Coverage Status](https://coveralls.io/repos/github/PeculiarVentures/pvutils/badge.svg?branch=master)](https://coveralls.io/github/PeculiarVentures/pvutils?branch=master)

`pvutils` is a set of common utility functions used in various Peculiar Ventures Javascript based projects.

Some example capabilities included in `pvutils` include:
- Converting dates into UTC,
- Converting an "ArrayBuffer" into a hexdecimal string,
- Converting a number from 2^base to 2^10,
- Converting a number from 2^10 to 2^base,
- Concatenate two ArrayBuffers,
- And more...
