# WorkOS Integration Setup Guide

This guide will help you set up WorkOS authentication for your DealDash application.

## Prerequisites

- WorkOS account (sign up at [workos.com](https://workos.com))
- Next.js application with the DealDash frontend

## Step 1: Create WorkOS Application

1. **Sign up/Login to WorkOS Dashboard**
   - Go to [dashboard.workos.com](https://dashboard.workos.com)
   - Create an account or sign in

2. **Create a New Application**
   - Click "Create Application"
   - Name: "DealDash"
   - Application Type: "Regular Web Application"

3. **Configure Redirect URIs**
   - Add your development URL: `http://localhost:3000/api/auth/callback`
   - Add your production URL: `https://yourdomain.com/api/auth/callback`

## Step 2: Get Your Credentials

From your WorkOS dashboard, copy:

1. **Client ID** - Found in Application Details
2. **API Key** - Found in API Keys section (create one if needed)

## Step 3: Configure Environment Variables

Update your `.env.local` file:

```env
# WorkOS Authentication
WORKOS_CLIENT_ID=client_01JXZW2016VA3CBHK1RDAZ0BZP
NEXT_PUBLIC_WORKOS_CLIENT_ID=client_01JXZW2016VA3CBHK1RDAZ0BZP
WORKOS_API_KEY=sk_test_your_actual_api_key_here
WORKOS_COOKIE_PASSWORD=your-32-character-cookie-password-here
```

### Generate Cookie Password

The cookie password must be at least 32 characters. You can generate one:

```bash
# Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Using OpenSSL
openssl rand -hex 32
```

## Step 4: Install Dependencies

The WorkOS AuthKit package is already included in package.json:

```bash
npm install @workos-inc/authkit-nextjs
```

## Step 5: Configure Authentication

The following files are already configured for WorkOS:

- `middleware.ts` - Handles authentication middleware
- `app/api/auth/[...workos]/route.ts` - WorkOS API routes
- `providers/AuthProvider.tsx` - Authentication context
- `app/login/page.tsx` - Login page with WorkOS

## Step 6: Test Authentication

1. **Start your development server:**
   ```bash
   npm run dev
   ```

2. **Navigate to login page:**
   - Go to `http://localhost:3000/login`
   - Click "Sign in with WorkOS"

3. **Complete authentication flow:**
   - You'll be redirected to WorkOS
   - Sign in or create an account
   - You'll be redirected back to your app

## Step 7: Configure Organizations (Optional)

For multi-tenant support:

1. **In WorkOS Dashboard:**
   - Go to Organizations
   - Create organizations for each dealership
   - Invite users to specific organizations

2. **Update your app:**
   - Organizations will map to dealerships
   - Users can be members of multiple organizations

## Features Enabled

With WorkOS integration, you get:

✅ **Single Sign-On (SSO)** - Enterprise SSO providers
✅ **Multi-factor Authentication** - Built-in MFA support  
✅ **Directory Sync** - Sync with Active Directory, Google Workspace, etc.
✅ **Audit Logs** - Track all authentication events
✅ **User Management** - Centralized user management
✅ **Organization Support** - Multi-tenant dealership support

## Troubleshooting

### Common Issues

1. **"WORKOS_CLIENT_ID environment variable is not set"**
   - Ensure both `WORKOS_CLIENT_ID` and `NEXT_PUBLIC_WORKOS_CLIENT_ID` are set
   - Restart your development server after adding environment variables

2. **"Invalid redirect URI"**
   - Check that your redirect URI in WorkOS matches exactly
   - For development: `http://localhost:3000/api/auth/callback`
   - For production: `https://yourdomain.com/api/auth/callback`

3. **"Invalid cookie password"**
   - Ensure `WORKOS_COOKIE_PASSWORD` is at least 32 characters
   - Generate a new one using the commands above

4. **Authentication loop**
   - Clear your browser cookies
   - Check that all environment variables are correct
   - Verify your WorkOS application configuration

### Debug Mode

Enable debug logging by adding to your `.env.local`:

```env
WORKOS_DEBUG=true
```

## Production Deployment

For production:

1. **Update environment variables:**
   - Use production WorkOS credentials
   - Set production redirect URIs
   - Use a secure cookie password

2. **Configure your domain:**
   - Add production redirect URI to WorkOS
   - Update CORS settings if needed

3. **SSL/HTTPS:**
   - Ensure your production app uses HTTPS
   - WorkOS requires HTTPS for production

## Next Steps

1. **Set up Organizations** - Map dealerships to WorkOS organizations
2. **Configure SSO** - Set up enterprise SSO for dealerships
3. **Enable Directory Sync** - Sync with existing user directories
4. **Set up Webhooks** - Listen for user/organization events
5. **Implement Role-Based Access** - Use WorkOS roles for permissions

## Support

- [WorkOS Documentation](https://workos.com/docs)
- [WorkOS Support](https://workos.com/support)
- [AuthKit Documentation](https://workos.com/docs/authkit)

## Security Best Practices

1. **Environment Variables:**
   - Never commit API keys to version control
   - Use different keys for development/production
   - Rotate keys regularly

2. **Cookie Security:**
   - Use a strong cookie password
   - Enable secure cookies in production
   - Set appropriate cookie expiration

3. **HTTPS:**
   - Always use HTTPS in production
   - Redirect HTTP to HTTPS
   - Use HSTS headers

Your DealDash application now has enterprise-grade authentication powered by WorkOS!
