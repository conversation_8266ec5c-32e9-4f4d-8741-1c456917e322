'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/providers/AuthProvider';

export default function LoginPage() {
  const { isAuthenticated, signIn, loading } = useAuth();
  const router = useRouter();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  const handleSignIn = () => {
    signIn();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-primary-600 mb-4">
            <span className="text-white font-bold text-2xl">D</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">DealDash</h1>
          <p className="text-gray-600 mb-8">Loading...</p>
          <div className="loading-spinner h-8 w-8 mx-auto" />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-600">
            <span className="text-white font-bold text-xl">D</span>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to DealDash
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Powersports dealership management platform with enterprise authentication
          </p>
        </div>

        <div className="mt-8 space-y-6">
          <div>
            <button
              onClick={handleSignIn}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
              </svg>
              Sign in with WorkOS
            </button>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Secure enterprise authentication powered by{' '}
              <a href="https://workos.com" target="_blank" rel="noopener noreferrer" className="font-medium text-primary-600 hover:text-primary-500">
                WorkOS
              </a>
            </p>
          </div>
        </div>

        {/* Features */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h3 className="text-sm font-medium text-blue-800 mb-2">Enterprise Features</h3>
          <div className="text-xs text-blue-700 space-y-1">
            <p>✓ Single Sign-On (SSO)</p>
            <p>✓ Multi-factor Authentication</p>
            <p>✓ Directory Sync</p>
            <p>✓ Audit Logs</p>
          </div>
        </div>

        {/* Development info */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">Development Mode</h3>
            <div className="text-xs text-yellow-700 space-y-1">
              <p>WorkOS Client ID: {process.env.NEXT_PUBLIC_WORKOS_CLIENT_ID}</p>
              <p>Click "Sign in with WorkOS" to authenticate</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
