import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { VeltProvider } from '@veltdev/react';
import { QueryProvider } from '../providers/QueryProvider';
import { AuthProvider } from '../providers/AuthProvider';
import { VeltCollaborationProvider } from '../providers/VeltProvider';
import { Toaster } from 'react-hot-toast';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'DealDash - Powersports Dealership Platform',
  description: 'AI-powered analytics and management platform for powersports dealerships',
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#3b82f6',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full bg-gray-50`}>
        <QueryProvider>
          <AuthProvider>
            <VeltProvider apiKey={process.env.NEXT_PUBLIC_VELT_API_KEY!}>
              <VeltCollaborationProvider>
                {children}
                <Toaster
                  position="top-right"
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: '#363636',
                      color: '#fff',
                    },
                    success: {
                      duration: 3000,
                      iconTheme: {
                        primary: '#10b981',
                        secondary: '#fff',
                      },
                    },
                    error: {
                      duration: 5000,
                      iconTheme: {
                        primary: '#ef4444',
                        secondary: '#fff',
                      },
                    },
                  }}
                />
              </VeltCollaborationProvider>
            </VeltProvider>
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
