# DealDash Frontend

Modern React dashboard for the DealDash powersports dealership management platform with real-time collaboration powered by Velt.dev.

## Features

- 🚀 **Next.js 14** with App Router and TypeScript
- 🎨 **Tailwind CSS** for styling
- 🤝 **Velt.dev Integration** for real-time collaboration
- 📊 **Interactive Charts** with Recharts
- 🔄 **Real-time Updates** with React Query
- 🎯 **Type Safety** with TypeScript
- 📱 **Responsive Design** for all devices

## Collaboration Features

- **Live Presence** - See who's online and where they're working
- **Contextual Comments** - Comment on any dashboard element
- **Real-time Cursors** - See live mouse movements
- **Reactions** - React to insights and data points
- **Notifications** - Get notified of team activity
- **User Mentions** - @mention team members in comments

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- DealDash backend API running
- Velt.dev account and API key

### Installation

1. **Clone and install dependencies:**
```bash
cd frontend
npm install
```

2. **Set up environment variables:**
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_VELT_API_KEY=your_velt_api_key_here
WORKOS_CLIENT_ID=your_workos_client_id_here
WORKOS_API_KEY=your_workos_api_key_here
WORKOS_COOKIE_PASSWORD=your_32_character_cookie_password_here
```

3. **Get Velt API Key:**
   - Sign up at [velt.dev](https://velt.dev)
   - Create a new project
   - Copy your API key to the environment file

4. **Set up WorkOS Authentication:**
   - Sign up at [workos.com](https://workos.com)
   - Create a new application
   - Copy your Client ID and API Key
   - See `WORKOS_SETUP.md` for detailed instructions

5. **Start the development server:**
```bash
npm run dev
```

6. **Open your browser:**
   - Navigate to [http://localhost:3000](http://localhost:3000)
   - Click "Sign in with WorkOS" to authenticate

## Project Structure

```
frontend/
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Main dashboard page
│   ├── login/            # Authentication page
│   ├── layout.tsx        # Root layout with providers
│   └── globals.css       # Global styles
├── components/            # React components
│   ├── dashboard/        # Dashboard-specific components
│   ├── layout/          # Layout components
│   └── ui/              # Reusable UI components
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
├── providers/            # React context providers
├── types/                # TypeScript type definitions
└── public/               # Static assets
```

## Key Components

### Dashboard Components

- **StatsGrid** - Key metrics with collaboration
- **SalesChart** - Interactive sales analytics
- **InventoryOverview** - Inventory insights and charts
- **AIInsightsPanel** - AI-powered recommendations
- **RecentActivity** - Team activity feed

### Collaboration Integration

- **VeltProvider** - Manages Velt initialization
- **Comments** - Contextual commenting system
- **Presence** - Live user presence indicators
- **Cursors** - Real-time cursor tracking

## API Integration

The frontend connects to the DealDash FastAPI backend:

- **Authentication** - JWT-based auth with refresh tokens
- **Dealership Data** - Multi-tenant dealership management
- **Inventory** - Real-time inventory tracking
- **Sales** - Sales analytics and insights
- **AI Insights** - AI-powered recommendations

## Velt Configuration

Velt is configured for multi-tenant use:

```typescript
// Each dealership is an organization
organizationId: dealership.id.toString()

// Users are identified with dealership context
userId: user.id.toString()
groupId: `dealership_${dealership.id}`

// Documents are scoped to dealership and page
documentId: `dashboard_${dealership.id}`
```

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks

### Code Style

- **TypeScript** - Strict mode enabled
- **ESLint** - Code linting and formatting
- **Prettier** - Code formatting
- **Tailwind CSS** - Utility-first styling

### Adding New Features

1. **Create component** in appropriate directory
2. **Add types** in `types/` directory
3. **Integrate Velt** for collaboration features
4. **Add API calls** in `lib/api.ts`
5. **Update navigation** if needed

## Deployment

### Build for Production

```bash
npm run build
npm run start
```

### Environment Variables

Production environment variables:

```env
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_VELT_API_KEY=your_production_velt_key
NEXT_PUBLIC_ENVIRONMENT=production
```

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## Troubleshooting

### Common Issues

1. **Velt not loading:**
   - Check API key is correct
   - Verify environment variables
   - Check browser console for errors

2. **API connection issues:**
   - Verify backend is running
   - Check CORS configuration
   - Verify API URL in environment

3. **Authentication problems:**
   - Clear browser cookies
   - Check token expiration
   - Verify backend auth endpoints

### Debug Mode

Enable debug logging:

```env
NEXT_PUBLIC_DEBUG=true
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is proprietary software for DealDash platform.

## Support

For support and questions:
- Check the troubleshooting section
- Review Velt.dev documentation
- Contact the development team
