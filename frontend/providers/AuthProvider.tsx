'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUser, useAuth as useWorkOSAuth } from '@workos-inc/authkit-nextjs';
import { User, Dealership } from '@/types/auth';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  workosUser: any;
  loading: boolean;
  signIn: () => void;
  signOut: () => void;
  isAuthenticated: boolean;
  dealerships: Dealership[];
  currentDealership: Dealership | null;
  switchDealership: (dealershipId: string) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock dealerships for demo
const mockDealerships: Dealership[] = [
  {
    id: 1,
    name: 'Demo Powersports',
    business_name: 'Demo Powersports LLC',
    country: 'US',
    is_active: true,
    subscription_tier: 'pro',
    created_at: new Date().toISOString(),
    lightspeed_connected: true,
    user_role: 'owner',
  },
];

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [dealerships, setDealerships] = useState<Dealership[]>(mockDealerships);
  const [currentDealership, setCurrentDealership] = useState<Dealership | null>(null);
  const [loading, setLoading] = useState(true);

  const { user: workosUser, isLoading: workosLoading } = useUser();
  const { signIn, signOut } = useWorkOSAuth();
  const router = useRouter();

  const isAuthenticated = !!workosUser;

  // Initialize auth state with WorkOS
  useEffect(() => {
    const initAuth = async () => {
      if (workosLoading) return;

      if (workosUser) {
        try {
          // Use mock dealerships for now
          const userDealerships = mockDealerships;
          setDealerships(userDealerships);

          // Set current dealership (first one)
          if (userDealerships.length > 0) {
            setCurrentDealership(userDealerships[0]);
          }

          // Create user object from WorkOS user
          const userData: User = {
            id: parseInt(workosUser.id) || 0,
            email: workosUser.email,
            first_name: workosUser.firstName || '',
            last_name: workosUser.lastName || '',
            full_name: `${workosUser.firstName || ''} ${workosUser.lastName || ''}`.trim(),
            avatar_url: workosUser.profilePictureUrl,
            is_active: true,
            is_verified: workosUser.emailVerified || false,
            created_at: new Date().toISOString(),
            current_dealership_id: userDealerships[0]?.id,
            dealership_role: userDealerships[0]?.user_role || 'viewer',
          };

          setUser(userData);
        } catch (error) {
          console.error('Failed to initialize user data:', error);
          toast.error('Failed to load user data');
        }
      } else {
        setUser(null);
        setDealerships([]);
        setCurrentDealership(null);
      }

      setLoading(false);
    };

    initAuth();
  }, [workosUser, workosLoading]);

  const handleSignIn = () => {
    signIn();
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setUser(null);
      setDealerships([]);
      setCurrentDealership(null);
      toast.success('Logged out successfully');
      router.push('/');
    } catch (error) {
      console.error('Sign out failed:', error);
      toast.error('Failed to sign out');
    }
  };

  const switchDealership = (dealershipId: string) => {
    const dealership = dealerships.find(d => d.id.toString() === dealershipId);
    if (dealership) {
      setCurrentDealership(dealership);

      // Update user's current dealership
      if (user) {
        setUser({
          ...user,
          current_dealership_id: dealership.id,
          dealership_role: dealership.user_role || 'viewer',
        });
      }

      toast.success(`Switched to ${dealership.name}`);
    }
  };

  const value = {
    user,
    workosUser,
    loading,
    signIn: handleSignIn,
    signOut: handleSignOut,
    isAuthenticated,
    dealerships,
    currentDealership,
    switchDealership,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
