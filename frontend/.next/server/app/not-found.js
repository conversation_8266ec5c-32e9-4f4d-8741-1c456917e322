/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?dc2f\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/home/<USER>/learning/python/dealdash/frontend/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fapp%2Fglobals.css&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Freact-hot-toast%402.5.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fproviders%2FAuthProvider.tsx&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fproviders%2FQueryProvider.tsx&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fproviders%2FVeltProvider.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fapp%2Fglobals.css&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Freact-hot-toast%402.5.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fproviders%2FAuthProvider.tsx&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fproviders%2FQueryProvider.tsx&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fproviders%2FVeltProvider.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/AuthProvider.tsx */ \"(ssr)/./providers/AuthProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/QueryProvider.tsx */ \"(ssr)/./providers/QueryProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/VeltProvider.tsx */ \"(ssr)/./providers/VeltProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fapp%2Fglobals.css&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fnode_modules%2F.pnpm%2Freact-hot-toast%402.5.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fproviders%2FAuthProvider.tsx&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fproviders%2FQueryProvider.tsx&modules=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fproviders%2FVeltProvider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./hooks/useDealership.ts":
/*!********************************!*\
  !*** ./hooks/useDealership.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDealership: () => (/* binding */ useDealership)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.80.7_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.80.7_react@18.3.1/node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.80.7_react@18.3.1/node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/AuthProvider */ \"(ssr)/./providers/AuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ useDealership auto */ \n\n\n\nfunction useDealership() {\n    const { user } = (0,_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [currentDealershipId, setCurrentDealershipId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    // Get user's dealerships\n    const { data: dealerships = [], isLoading: dealershipsLoading, error: dealershipsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"dealerships\",\n            \"user\"\n        ],\n        queryFn: _lib_api__WEBPACK_IMPORTED_MODULE_0__.dealershipApi.getUserDealerships,\n        enabled: !!user\n    });\n    // Set initial dealership\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (dealerships.length > 0 && !currentDealershipId) {\n            // Use user's preferred dealership or first available\n            const preferredId = user?.current_dealership_id;\n            const dealership = preferredId ? dealerships.find((d)=>d.id === preferredId) : dealerships[0];\n            if (dealership) {\n                setCurrentDealershipId(dealership.id);\n            }\n        }\n    }, [\n        dealerships,\n        currentDealershipId,\n        user\n    ]);\n    // Get current dealership details\n    const { data: currentDealership, isLoading: currentDealershipLoading, error: currentDealershipError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"dealership\",\n            currentDealershipId\n        ],\n        queryFn: ()=>_lib_api__WEBPACK_IMPORTED_MODULE_0__.dealershipApi.getDealership(currentDealershipId),\n        enabled: !!currentDealershipId\n    });\n    // Update dealership mutation\n    const updateDealershipMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: ({ id, data })=>_lib_api__WEBPACK_IMPORTED_MODULE_0__.dealershipApi.updateDealership(id, data),\n        onSuccess: (data, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"dealership\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"dealerships\",\n                    \"user\"\n                ]\n            });\n        }\n    });\n    const switchDealership = (dealershipId)=>{\n        setCurrentDealershipId(dealershipId);\n    };\n    const updateDealership = (data)=>{\n        if (!currentDealershipId) return;\n        return updateDealershipMutation.mutateAsync({\n            id: currentDealershipId,\n            data\n        });\n    };\n    return {\n        // Data\n        dealerships,\n        currentDealership,\n        currentDealershipId,\n        // Loading states\n        dealershipsLoading,\n        currentDealershipLoading,\n        isLoading: dealershipsLoading || currentDealershipLoading,\n        // Errors\n        dealershipsError,\n        currentDealershipError,\n        error: dealershipsError || currentDealershipError,\n        // Actions\n        switchDealership,\n        updateDealership,\n        // Mutation states\n        isUpdating: updateDealershipMutation.isPending,\n        updateError: updateDealershipMutation.error\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useDealership.ts\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsApi: () => (/* binding */ analyticsApi),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   dealershipApi: () => (/* binding */ dealershipApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   inventoryApi: () => (/* binding */ inventoryApi),\n/* harmony export */   lightspeedApi: () => (/* binding */ lightspeedApi),\n/* harmony export */   salesApi: () => (/* binding */ salesApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/.pnpm/js-cookie@3.0.5/node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n    baseURL: \"http://localhost:8000\" || 0,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napi.interceptors.response.use((response)=>response, async (error)=>{\n    const originalRequest = error.config;\n    // Handle 401 errors (unauthorized)\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"refresh_token\");\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(`${api.defaults.baseURL}/api/v1/auth/refresh`, {\n                    refresh_token: refreshToken\n                });\n                const { access_token, refresh_token: newRefreshToken } = response.data;\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", access_token, {\n                    expires: 1\n                });\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refresh_token\", newRefreshToken, {\n                    expires: 7\n                });\n                // Retry original request\n                originalRequest.headers.Authorization = `Bearer ${access_token}`;\n                return api(originalRequest);\n            }\n        } catch (refreshError) {\n            // Refresh failed, redirect to login\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"access_token\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refresh_token\");\n            window.location.href = \"/login\";\n            return Promise.reject(refreshError);\n        }\n    }\n    // Handle other errors\n    if (error.response?.status >= 500) {\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(\"Server error. Please try again later.\");\n    } else if (error.response?.status === 403) {\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(\"You do not have permission to perform this action.\");\n    } else if (error.response?.status === 404) {\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(\"Resource not found.\");\n    }\n    return Promise.reject(error);\n});\n// Auth API\nconst authApi = {\n    login: async (email, password)=>{\n        const response = await api.post(\"/api/v1/auth/login\", {\n            email,\n            password\n        });\n        return response.data;\n    },\n    refreshToken: async (refreshToken)=>{\n        const response = await api.post(\"/api/v1/auth/refresh\", {\n            refresh_token: refreshToken\n        });\n        return response.data;\n    },\n    getCurrentUser: async ()=>{\n        const response = await api.get(\"/api/v1/users/me\");\n        return response.data;\n    },\n    updateProfile: async (data)=>{\n        const response = await api.put(\"/api/v1/users/me\", data);\n        return response.data;\n    }\n};\n// Dealership API\nconst dealershipApi = {\n    getUserDealerships: async ()=>{\n        const response = await api.get(\"/api/v1/dealerships/me\");\n        return response.data;\n    },\n    getDealership: async (id)=>{\n        const response = await api.get(`/api/v1/dealerships/${id}`);\n        return response.data;\n    },\n    updateDealership: async (id, data)=>{\n        const response = await api.put(`/api/v1/dealerships/${id}`, data);\n        return response.data;\n    }\n};\n// Inventory API\nconst inventoryApi = {\n    getItems: async (dealershipId, params)=>{\n        const response = await api.get(`/api/v1/inventory/${dealershipId}`, {\n            params\n        });\n        return response.data;\n    },\n    getItem: async (dealershipId, itemId)=>{\n        const response = await api.get(`/api/v1/inventory/${dealershipId}/${itemId}`);\n        return response.data;\n    },\n    updateItem: async (dealershipId, itemId, data)=>{\n        const response = await api.put(`/api/v1/inventory/${dealershipId}/${itemId}`, data);\n        return response.data;\n    },\n    getCategories: async (dealershipId)=>{\n        const response = await api.get(`/api/v1/inventory/${dealershipId}/categories`);\n        return response.data;\n    }\n};\n// Sales API\nconst salesApi = {\n    getSales: async (dealershipId, params)=>{\n        const response = await api.get(`/api/v1/sales/${dealershipId}`, {\n            params\n        });\n        return response.data;\n    },\n    getSale: async (dealershipId, saleId)=>{\n        const response = await api.get(`/api/v1/sales/${dealershipId}/${saleId}`);\n        return response.data;\n    },\n    getMetrics: async (dealershipId, params)=>{\n        const response = await api.get(`/api/v1/sales/${dealershipId}/metrics`, {\n            params\n        });\n        return response.data;\n    }\n};\n// Analytics API\nconst analyticsApi = {\n    getDashboardData: async (dealershipId)=>{\n        const response = await api.get(`/api/v1/analytics/dashboard/${dealershipId}`);\n        return response.data;\n    },\n    getInsights: async (dealershipId)=>{\n        const response = await api.get(`/api/v1/analytics/insights/${dealershipId}`);\n        return response.data;\n    },\n    getInventoryInsights: async (dealershipId, params)=>{\n        const response = await api.get(`/api/v1/analytics/inventory-insights/${dealershipId}`, {\n            params\n        });\n        return response.data;\n    },\n    getSalesInsights: async (dealershipId, params)=>{\n        const response = await api.get(`/api/v1/analytics/sales-insights/${dealershipId}`, {\n            params\n        });\n        return response.data;\n    },\n    triggerProcessing: async (dealershipId)=>{\n        const response = await api.post(`/api/v1/analytics/process/${dealershipId}`);\n        return response.data;\n    },\n    generateInsights: async (dealershipId, type)=>{\n        const response = await api.post(`/api/v1/analytics/generate-insights/${dealershipId}?insight_type=${type}`);\n        return response.data;\n    }\n};\n// Lightspeed API\nconst lightspeedApi = {\n    getAuthUrl: async (dealershipId)=>{\n        const response = await api.get(`/api/v1/lightspeed/auth-url?dealership_id=${dealershipId}`);\n        return response.data;\n    },\n    getStatus: async (dealershipId)=>{\n        const response = await api.get(`/api/v1/lightspeed/status/${dealershipId}`);\n        return response.data;\n    },\n    triggerSync: async (dealershipId, syncType = \"manual\")=>{\n        const response = await api.post(`/api/v1/lightspeed/sync/${dealershipId}`, {\n            sync_type: syncType\n        });\n        return response.data;\n    },\n    getSyncLogs: async (dealershipId, params)=>{\n        const response = await api.get(`/api/v1/lightspeed/sync-logs/${dealershipId}`, {\n            params\n        });\n        return response.data;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./providers/AuthProvider.tsx":
/*!************************************!*\
  !*** ./providers/AuthProvider.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _workos_inc_authkit_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @workos-inc/authkit-nextjs */ \"(ssr)/./node_modules/.pnpm/@workos-inc+authkit-nextjs@0.10.1_next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3_1bb817457dba2a05273873c5a29d727e/node_modules/@workos-inc/authkit-nextjs/dist/cjs/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Mock dealerships for demo\nconst mockDealerships = [\n    {\n        id: 1,\n        name: \"Demo Powersports\",\n        business_name: \"Demo Powersports LLC\",\n        country: \"US\",\n        is_active: true,\n        subscription_tier: \"pro\",\n        created_at: new Date().toISOString(),\n        lightspeed_connected: true,\n        user_role: \"owner\"\n    }\n];\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dealerships, setDealerships] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockDealerships);\n    const [currentDealership, setCurrentDealership] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { user: workosUser, isLoading: workosLoading } = (0,_workos_inc_authkit_nextjs__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const { signIn, signOut } = (0,_workos_inc_authkit_nextjs__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAuthenticated = !!workosUser;\n    // Initialize auth state with WorkOS\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            if (workosLoading) return;\n            if (workosUser) {\n                try {\n                    // Use mock dealerships for now\n                    const userDealerships = mockDealerships;\n                    setDealerships(userDealerships);\n                    // Set current dealership (first one)\n                    if (userDealerships.length > 0) {\n                        setCurrentDealership(userDealerships[0]);\n                    }\n                    // Create user object from WorkOS user\n                    const userData = {\n                        id: parseInt(workosUser.id) || 0,\n                        email: workosUser.email,\n                        first_name: workosUser.firstName || \"\",\n                        last_name: workosUser.lastName || \"\",\n                        full_name: `${workosUser.firstName || \"\"} ${workosUser.lastName || \"\"}`.trim(),\n                        avatar_url: workosUser.profilePictureUrl,\n                        is_active: true,\n                        is_verified: workosUser.emailVerified || false,\n                        created_at: new Date().toISOString(),\n                        current_dealership_id: userDealerships[0]?.id,\n                        dealership_role: userDealerships[0]?.user_role || \"viewer\"\n                    };\n                    setUser(userData);\n                } catch (error) {\n                    console.error(\"Failed to initialize user data:\", error);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Failed to load user data\");\n                }\n            } else {\n                setUser(null);\n                setDealerships([]);\n                setCurrentDealership(null);\n            }\n            setLoading(false);\n        };\n        initAuth();\n    }, [\n        workosUser,\n        workosLoading\n    ]);\n    const handleSignIn = ()=>{\n        signIn();\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            setUser(null);\n            setDealerships([]);\n            setCurrentDealership(null);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Logged out successfully\");\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Sign out failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Failed to sign out\");\n        }\n    };\n    const switchDealership = (dealershipId)=>{\n        const dealership = dealerships.find((d)=>d.id.toString() === dealershipId);\n        if (dealership) {\n            setCurrentDealership(dealership);\n            // Update user's current dealership\n            if (user) {\n                setUser({\n                    ...user,\n                    current_dealership_id: dealership.id,\n                    dealership_role: dealership.user_role || \"viewer\"\n                });\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(`Switched to ${dealership.name}`);\n        }\n    };\n    const value = {\n        user,\n        workosUser,\n        loading,\n        signIn: handleSignIn,\n        signOut: handleSignOut,\n        isAuthenticated,\n        dealerships,\n        currentDealership,\n        switchDealership\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/learning/python/dealdash/frontend/providers/AuthProvider.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvQXV0aFByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUV1RTtBQUMzQjtBQUNtQztBQUUzQztBQWNwQyxNQUFNUyw0QkFBY1Qsb0RBQWFBLENBQThCVTtBQUUvRCw0QkFBNEI7QUFDNUIsTUFBTUMsa0JBQWdDO0lBQ3BDO1FBQ0VDLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxlQUFlO1FBQ2ZDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxtQkFBbUI7UUFDbkJDLFlBQVksSUFBSUMsT0FBT0MsV0FBVztRQUNsQ0Msc0JBQXNCO1FBQ3RCQyxXQUFXO0lBQ2I7Q0FDRDtBQUVNLFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUFpQztJQUN0RSxNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR3ZCLCtDQUFRQSxDQUFjO0lBQzlDLE1BQU0sQ0FBQ3dCLGFBQWFDLGVBQWUsR0FBR3pCLCtDQUFRQSxDQUFlUTtJQUM3RCxNQUFNLENBQUNrQixtQkFBbUJDLHFCQUFxQixHQUFHM0IsK0NBQVFBLENBQW9CO0lBQzlFLE1BQU0sQ0FBQzRCLFNBQVNDLFdBQVcsR0FBRzdCLCtDQUFRQSxDQUFDO0lBRXZDLE1BQU0sRUFBRXNCLE1BQU1RLFVBQVUsRUFBRUMsV0FBV0MsYUFBYSxFQUFFLEdBQUc5QixtRUFBT0E7SUFDOUQsTUFBTSxFQUFFK0IsTUFBTSxFQUFFQyxPQUFPLEVBQUUsR0FBRzlCLG1FQUFhQTtJQUN6QyxNQUFNK0IsU0FBU2xDLDBEQUFTQTtJQUV4QixNQUFNbUMsa0JBQWtCLENBQUMsQ0FBQ047SUFFMUIsb0NBQW9DO0lBQ3BDL0IsZ0RBQVNBLENBQUM7UUFDUixNQUFNc0MsV0FBVztZQUNmLElBQUlMLGVBQWU7WUFFbkIsSUFBSUYsWUFBWTtnQkFDZCxJQUFJO29CQUNGLCtCQUErQjtvQkFDL0IsTUFBTVEsa0JBQWtCOUI7b0JBQ3hCaUIsZUFBZWE7b0JBRWYscUNBQXFDO29CQUNyQyxJQUFJQSxnQkFBZ0JDLE1BQU0sR0FBRyxHQUFHO3dCQUM5QloscUJBQXFCVyxlQUFlLENBQUMsRUFBRTtvQkFDekM7b0JBRUEsc0NBQXNDO29CQUN0QyxNQUFNRSxXQUFpQjt3QkFDckIvQixJQUFJZ0MsU0FBU1gsV0FBV3JCLEVBQUUsS0FBSzt3QkFDL0JpQyxPQUFPWixXQUFXWSxLQUFLO3dCQUN2QkMsWUFBWWIsV0FBV2MsU0FBUyxJQUFJO3dCQUNwQ0MsV0FBV2YsV0FBV2dCLFFBQVEsSUFBSTt3QkFDbENDLFdBQVcsQ0FBQyxFQUFFakIsV0FBV2MsU0FBUyxJQUFJLEdBQUcsQ0FBQyxFQUFFZCxXQUFXZ0IsUUFBUSxJQUFJLEdBQUcsQ0FBQyxDQUFDRSxJQUFJO3dCQUM1RUMsWUFBWW5CLFdBQVdvQixpQkFBaUI7d0JBQ3hDckMsV0FBVzt3QkFDWHNDLGFBQWFyQixXQUFXc0IsYUFBYSxJQUFJO3dCQUN6Q3JDLFlBQVksSUFBSUMsT0FBT0MsV0FBVzt3QkFDbENvQyx1QkFBdUJmLGVBQWUsQ0FBQyxFQUFFLEVBQUU3Qjt3QkFDM0M2QyxpQkFBaUJoQixlQUFlLENBQUMsRUFBRSxFQUFFbkIsYUFBYTtvQkFDcEQ7b0JBRUFJLFFBQVFpQjtnQkFDVixFQUFFLE9BQU9lLE9BQU87b0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxtQ0FBbUNBO29CQUNqRGxELHVEQUFLQSxDQUFDa0QsS0FBSyxDQUFDO2dCQUNkO1lBQ0YsT0FBTztnQkFDTGhDLFFBQVE7Z0JBQ1JFLGVBQWUsRUFBRTtnQkFDakJFLHFCQUFxQjtZQUN2QjtZQUVBRSxXQUFXO1FBQ2I7UUFFQVE7SUFDRixHQUFHO1FBQUNQO1FBQVlFO0tBQWM7SUFFOUIsTUFBTXlCLGVBQWU7UUFDbkJ4QjtJQUNGO0lBRUEsTUFBTXlCLGdCQUFnQjtRQUNwQixJQUFJO1lBQ0YsTUFBTXhCO1lBQ05YLFFBQVE7WUFDUkUsZUFBZSxFQUFFO1lBQ2pCRSxxQkFBcUI7WUFDckJ0Qix1REFBS0EsQ0FBQ3NELE9BQU8sQ0FBQztZQUNkeEIsT0FBT3lCLElBQUksQ0FBQztRQUNkLEVBQUUsT0FBT0wsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsb0JBQW9CQTtZQUNsQ2xELHVEQUFLQSxDQUFDa0QsS0FBSyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU1NLG1CQUFtQixDQUFDQztRQUN4QixNQUFNQyxhQUFhdkMsWUFBWXdDLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXhELEVBQUUsQ0FBQ3lELFFBQVEsT0FBT0o7UUFDN0QsSUFBSUMsWUFBWTtZQUNkcEMscUJBQXFCb0M7WUFFckIsbUNBQW1DO1lBQ25DLElBQUl6QyxNQUFNO2dCQUNSQyxRQUFRO29CQUNOLEdBQUdELElBQUk7b0JBQ1ArQix1QkFBdUJVLFdBQVd0RCxFQUFFO29CQUNwQzZDLGlCQUFpQlMsV0FBVzVDLFNBQVMsSUFBSTtnQkFDM0M7WUFDRjtZQUVBZCx1REFBS0EsQ0FBQ3NELE9BQU8sQ0FBQyxDQUFDLFlBQVksRUFBRUksV0FBV3JELElBQUksQ0FBQyxDQUFDO1FBQ2hEO0lBQ0Y7SUFFQSxNQUFNeUQsUUFBUTtRQUNaN0M7UUFDQVE7UUFDQUY7UUFDQUssUUFBUXdCO1FBQ1J2QixTQUFTd0I7UUFDVHRCO1FBQ0FaO1FBQ0FFO1FBQ0FtQztJQUNGO0lBRUEscUJBQ0UsOERBQUN2RCxZQUFZOEQsUUFBUTtRQUFDRCxPQUFPQTtrQkFDMUI5Qzs7Ozs7O0FBR1A7QUFFTyxTQUFTbEI7SUFDZCxNQUFNa0UsVUFBVXZFLGlEQUFVQSxDQUFDUTtJQUMzQixJQUFJK0QsWUFBWTlELFdBQVc7UUFDekIsTUFBTSxJQUFJK0QsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWFsZGFzaC1mcm9udGVuZC8uL3Byb3ZpZGVycy9BdXRoUHJvdmlkZXIudHN4PzZhMWMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHVzZVVzZXIsIHVzZUF1dGggYXMgdXNlV29ya09TQXV0aCB9IGZyb20gJ0B3b3Jrb3MtaW5jL2F1dGhraXQtbmV4dGpzJztcbmltcG9ydCB7IFVzZXIsIERlYWxlcnNoaXAgfSBmcm9tICdAL3R5cGVzL2F1dGgnO1xuaW1wb3J0IHRvYXN0IGZyb20gJ3JlYWN0LWhvdC10b2FzdCc7XG5cbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xuICB1c2VyOiBVc2VyIHwgbnVsbDtcbiAgd29ya29zVXNlcjogYW55O1xuICBsb2FkaW5nOiBib29sZWFuO1xuICBzaWduSW46ICgpID0+IHZvaWQ7XG4gIHNpZ25PdXQ6ICgpID0+IHZvaWQ7XG4gIGlzQXV0aGVudGljYXRlZDogYm9vbGVhbjtcbiAgZGVhbGVyc2hpcHM6IERlYWxlcnNoaXBbXTtcbiAgY3VycmVudERlYWxlcnNoaXA6IERlYWxlcnNoaXAgfCBudWxsO1xuICBzd2l0Y2hEZWFsZXJzaGlwOiAoZGVhbGVyc2hpcElkOiBzdHJpbmcpID0+IHZvaWQ7XG59XG5cbmNvbnN0IEF1dGhDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBdXRoQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbi8vIE1vY2sgZGVhbGVyc2hpcHMgZm9yIGRlbW9cbmNvbnN0IG1vY2tEZWFsZXJzaGlwczogRGVhbGVyc2hpcFtdID0gW1xuICB7XG4gICAgaWQ6IDEsXG4gICAgbmFtZTogJ0RlbW8gUG93ZXJzcG9ydHMnLFxuICAgIGJ1c2luZXNzX25hbWU6ICdEZW1vIFBvd2Vyc3BvcnRzIExMQycsXG4gICAgY291bnRyeTogJ1VTJyxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgc3Vic2NyaXB0aW9uX3RpZXI6ICdwcm8nLFxuICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICBsaWdodHNwZWVkX2Nvbm5lY3RlZDogdHJ1ZSxcbiAgICB1c2VyX3JvbGU6ICdvd25lcicsXG4gIH0sXG5dO1xuXG5leHBvcnQgZnVuY3Rpb24gQXV0aFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGU8VXNlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbZGVhbGVyc2hpcHMsIHNldERlYWxlcnNoaXBzXSA9IHVzZVN0YXRlPERlYWxlcnNoaXBbXT4obW9ja0RlYWxlcnNoaXBzKTtcbiAgY29uc3QgW2N1cnJlbnREZWFsZXJzaGlwLCBzZXRDdXJyZW50RGVhbGVyc2hpcF0gPSB1c2VTdGF0ZTxEZWFsZXJzaGlwIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIGNvbnN0IHsgdXNlcjogd29ya29zVXNlciwgaXNMb2FkaW5nOiB3b3Jrb3NMb2FkaW5nIH0gPSB1c2VVc2VyKCk7XG4gIGNvbnN0IHsgc2lnbkluLCBzaWduT3V0IH0gPSB1c2VXb3JrT1NBdXRoKCk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuXG4gIGNvbnN0IGlzQXV0aGVudGljYXRlZCA9ICEhd29ya29zVXNlcjtcblxuICAvLyBJbml0aWFsaXplIGF1dGggc3RhdGUgd2l0aCBXb3JrT1NcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBpbml0QXV0aCA9IGFzeW5jICgpID0+IHtcbiAgICAgIGlmICh3b3Jrb3NMb2FkaW5nKSByZXR1cm47XG5cbiAgICAgIGlmICh3b3Jrb3NVc2VyKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgLy8gVXNlIG1vY2sgZGVhbGVyc2hpcHMgZm9yIG5vd1xuICAgICAgICAgIGNvbnN0IHVzZXJEZWFsZXJzaGlwcyA9IG1vY2tEZWFsZXJzaGlwcztcbiAgICAgICAgICBzZXREZWFsZXJzaGlwcyh1c2VyRGVhbGVyc2hpcHMpO1xuXG4gICAgICAgICAgLy8gU2V0IGN1cnJlbnQgZGVhbGVyc2hpcCAoZmlyc3Qgb25lKVxuICAgICAgICAgIGlmICh1c2VyRGVhbGVyc2hpcHMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgc2V0Q3VycmVudERlYWxlcnNoaXAodXNlckRlYWxlcnNoaXBzWzBdKTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBDcmVhdGUgdXNlciBvYmplY3QgZnJvbSBXb3JrT1MgdXNlclxuICAgICAgICAgIGNvbnN0IHVzZXJEYXRhOiBVc2VyID0ge1xuICAgICAgICAgICAgaWQ6IHBhcnNlSW50KHdvcmtvc1VzZXIuaWQpIHx8IDAsXG4gICAgICAgICAgICBlbWFpbDogd29ya29zVXNlci5lbWFpbCxcbiAgICAgICAgICAgIGZpcnN0X25hbWU6IHdvcmtvc1VzZXIuZmlyc3ROYW1lIHx8ICcnLFxuICAgICAgICAgICAgbGFzdF9uYW1lOiB3b3Jrb3NVc2VyLmxhc3ROYW1lIHx8ICcnLFxuICAgICAgICAgICAgZnVsbF9uYW1lOiBgJHt3b3Jrb3NVc2VyLmZpcnN0TmFtZSB8fCAnJ30gJHt3b3Jrb3NVc2VyLmxhc3ROYW1lIHx8ICcnfWAudHJpbSgpLFxuICAgICAgICAgICAgYXZhdGFyX3VybDogd29ya29zVXNlci5wcm9maWxlUGljdHVyZVVybCxcbiAgICAgICAgICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICAgICAgICAgIGlzX3ZlcmlmaWVkOiB3b3Jrb3NVc2VyLmVtYWlsVmVyaWZpZWQgfHwgZmFsc2UsXG4gICAgICAgICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICBjdXJyZW50X2RlYWxlcnNoaXBfaWQ6IHVzZXJEZWFsZXJzaGlwc1swXT8uaWQsXG4gICAgICAgICAgICBkZWFsZXJzaGlwX3JvbGU6IHVzZXJEZWFsZXJzaGlwc1swXT8udXNlcl9yb2xlIHx8ICd2aWV3ZXInLFxuICAgICAgICAgIH07XG5cbiAgICAgICAgICBzZXRVc2VyKHVzZXJEYXRhKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gaW5pdGlhbGl6ZSB1c2VyIGRhdGE6JywgZXJyb3IpO1xuICAgICAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gbG9hZCB1c2VyIGRhdGEnKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0VXNlcihudWxsKTtcbiAgICAgICAgc2V0RGVhbGVyc2hpcHMoW10pO1xuICAgICAgICBzZXRDdXJyZW50RGVhbGVyc2hpcChudWxsKTtcbiAgICAgIH1cblxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfTtcblxuICAgIGluaXRBdXRoKCk7XG4gIH0sIFt3b3Jrb3NVc2VyLCB3b3Jrb3NMb2FkaW5nXSk7XG5cbiAgY29uc3QgaGFuZGxlU2lnbkluID0gKCkgPT4ge1xuICAgIHNpZ25JbigpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNpZ25PdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHNpZ25PdXQoKTtcbiAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICBzZXREZWFsZXJzaGlwcyhbXSk7XG4gICAgICBzZXRDdXJyZW50RGVhbGVyc2hpcChudWxsKTtcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ0xvZ2dlZCBvdXQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgICByb3V0ZXIucHVzaCgnLycpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdTaWduIG91dCBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgdG9hc3QuZXJyb3IoJ0ZhaWxlZCB0byBzaWduIG91dCcpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBzd2l0Y2hEZWFsZXJzaGlwID0gKGRlYWxlcnNoaXBJZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgZGVhbGVyc2hpcCA9IGRlYWxlcnNoaXBzLmZpbmQoZCA9PiBkLmlkLnRvU3RyaW5nKCkgPT09IGRlYWxlcnNoaXBJZCk7XG4gICAgaWYgKGRlYWxlcnNoaXApIHtcbiAgICAgIHNldEN1cnJlbnREZWFsZXJzaGlwKGRlYWxlcnNoaXApO1xuXG4gICAgICAvLyBVcGRhdGUgdXNlcidzIGN1cnJlbnQgZGVhbGVyc2hpcFxuICAgICAgaWYgKHVzZXIpIHtcbiAgICAgICAgc2V0VXNlcih7XG4gICAgICAgICAgLi4udXNlcixcbiAgICAgICAgICBjdXJyZW50X2RlYWxlcnNoaXBfaWQ6IGRlYWxlcnNoaXAuaWQsXG4gICAgICAgICAgZGVhbGVyc2hpcF9yb2xlOiBkZWFsZXJzaGlwLnVzZXJfcm9sZSB8fCAndmlld2VyJyxcbiAgICAgICAgfSk7XG4gICAgICB9XG5cbiAgICAgIHRvYXN0LnN1Y2Nlc3MoYFN3aXRjaGVkIHRvICR7ZGVhbGVyc2hpcC5uYW1lfWApO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCB2YWx1ZSA9IHtcbiAgICB1c2VyLFxuICAgIHdvcmtvc1VzZXIsXG4gICAgbG9hZGluZyxcbiAgICBzaWduSW46IGhhbmRsZVNpZ25JbixcbiAgICBzaWduT3V0OiBoYW5kbGVTaWduT3V0LFxuICAgIGlzQXV0aGVudGljYXRlZCxcbiAgICBkZWFsZXJzaGlwcyxcbiAgICBjdXJyZW50RGVhbGVyc2hpcCxcbiAgICBzd2l0Y2hEZWFsZXJzaGlwLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUF1dGgoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VSb3V0ZXIiLCJ1c2VVc2VyIiwidXNlQXV0aCIsInVzZVdvcmtPU0F1dGgiLCJ0b2FzdCIsIkF1dGhDb250ZXh0IiwidW5kZWZpbmVkIiwibW9ja0RlYWxlcnNoaXBzIiwiaWQiLCJuYW1lIiwiYnVzaW5lc3NfbmFtZSIsImNvdW50cnkiLCJpc19hY3RpdmUiLCJzdWJzY3JpcHRpb25fdGllciIsImNyZWF0ZWRfYXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJsaWdodHNwZWVkX2Nvbm5lY3RlZCIsInVzZXJfcm9sZSIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJkZWFsZXJzaGlwcyIsInNldERlYWxlcnNoaXBzIiwiY3VycmVudERlYWxlcnNoaXAiLCJzZXRDdXJyZW50RGVhbGVyc2hpcCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwid29ya29zVXNlciIsImlzTG9hZGluZyIsIndvcmtvc0xvYWRpbmciLCJzaWduSW4iLCJzaWduT3V0Iiwicm91dGVyIiwiaXNBdXRoZW50aWNhdGVkIiwiaW5pdEF1dGgiLCJ1c2VyRGVhbGVyc2hpcHMiLCJsZW5ndGgiLCJ1c2VyRGF0YSIsInBhcnNlSW50IiwiZW1haWwiLCJmaXJzdF9uYW1lIiwiZmlyc3ROYW1lIiwibGFzdF9uYW1lIiwibGFzdE5hbWUiLCJmdWxsX25hbWUiLCJ0cmltIiwiYXZhdGFyX3VybCIsInByb2ZpbGVQaWN0dXJlVXJsIiwiaXNfdmVyaWZpZWQiLCJlbWFpbFZlcmlmaWVkIiwiY3VycmVudF9kZWFsZXJzaGlwX2lkIiwiZGVhbGVyc2hpcF9yb2xlIiwiZXJyb3IiLCJjb25zb2xlIiwiaGFuZGxlU2lnbkluIiwiaGFuZGxlU2lnbk91dCIsInN1Y2Nlc3MiLCJwdXNoIiwic3dpdGNoRGVhbGVyc2hpcCIsImRlYWxlcnNoaXBJZCIsImRlYWxlcnNoaXAiLCJmaW5kIiwiZCIsInRvU3RyaW5nIiwidmFsdWUiLCJQcm92aWRlciIsImNvbnRleHQiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./providers/QueryProvider.tsx":
/*!*************************************!*\
  !*** ./providers/QueryProvider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-core@5.80.7/node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.80.7_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@18.3.1__react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n\n\n\nfunction QueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 5 * 60 * 1000,\n                    gcTime: 10 * 60 * 1000,\n                    retry: (failureCount, error)=>{\n                        // Don't retry on 4xx errors\n                        if (error?.response?.status >= 400 && error?.response?.status < 500) {\n                            return false;\n                        }\n                        return failureCount < 3;\n                    },\n                    refetchOnWindowFocus: false\n                },\n                mutations: {\n                    retry: 1\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"/home/<USER>/learning/python/dealdash/frontend/providers/QueryProvider.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/learning/python/dealdash/frontend/providers/QueryProvider.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./providers/QueryProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./providers/VeltProvider.tsx":
/*!************************************!*\
  !*** ./providers/VeltProvider.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VeltCollaborationProvider: () => (/* binding */ VeltCollaborationProvider),\n/* harmony export */   useVeltCollaboration: () => (/* binding */ useVeltCollaboration)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _veltdev_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @veltdev/react */ \"(ssr)/./node_modules/.pnpm/@veltdev+react@1.0.164_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@veltdev/react/esm/index.js\");\n/* harmony import */ var _AuthProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthProvider */ \"(ssr)/./providers/AuthProvider.tsx\");\n/* harmony import */ var _hooks_useDealership__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDealership */ \"(ssr)/./hooks/useDealership.ts\");\n/* __next_internal_client_entry_do_not_use__ VeltCollaborationProvider,useVeltCollaboration auto */ \n\n\n\n\nconst VeltContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction VeltCollaborationProvider({ children }) {\n    const { user, isAuthenticated } = (0,_AuthProvider__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { currentDealership } = (0,_hooks_useDealership__WEBPACK_IMPORTED_MODULE_4__.useDealership)();\n    const { client } = (0,_veltdev_react__WEBPACK_IMPORTED_MODULE_2__.useVeltClient)();\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize Velt when user and dealership are available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!client || !isAuthenticated || !user || !currentDealership) {\n            setIsInitialized(false);\n            return;\n        }\n        const initializeVelt = async ()=>{\n            try {\n                // Identify user with Velt\n                await client.identify({\n                    userId: user.id.toString(),\n                    name: `${user.first_name} ${user.last_name}`,\n                    email: user.email,\n                    avatar: user.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.first_name + \" \" + user.last_name)}&background=3b82f6&color=fff`,\n                    organizationId: currentDealership.id.toString(),\n                    groupId: `dealership_${currentDealership.id}`,\n                    metadata: {\n                        dealershipId: currentDealership.id,\n                        dealershipName: currentDealership.name,\n                        userRole: user.dealership_role,\n                        permissions: user.permissions || []\n                    }\n                });\n                // Set organization context\n                await client.setOrganization({\n                    organizationId: currentDealership.id.toString(),\n                    organizationName: currentDealership.name,\n                    metadata: {\n                        dealershipId: currentDealership.id,\n                        subscriptionTier: currentDealership.subscription_tier\n                    }\n                });\n                setIsInitialized(true);\n                console.log(\"Velt initialized successfully\");\n            } catch (error) {\n                console.error(\"Failed to initialize Velt:\", error);\n                setIsInitialized(false);\n            }\n        };\n        initializeVelt();\n    }, [\n        client,\n        isAuthenticated,\n        user,\n        currentDealership\n    ]);\n    const setDocument = (documentId, metadata)=>{\n        if (!client || !isInitialized) return;\n        client.setDocument({\n            documentId,\n            organizationId: currentDealership?.id.toString(),\n            metadata: {\n                dealershipId: currentDealership?.id,\n                documentType: \"dashboard\",\n                ...metadata\n            }\n        });\n    };\n    const setLocation = (location)=>{\n        if (!client || !isInitialized) return;\n        client.setLocation({\n            location,\n            organizationId: currentDealership?.id.toString()\n        });\n    };\n    const generateElementId = (type, id, context)=>{\n        const baseId = `${type}_${id}`;\n        return context ? `${baseId}_${context}` : baseId;\n    };\n    const value = {\n        isInitialized,\n        setDocument,\n        setLocation,\n        generateElementId\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VeltContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/learning/python/dealdash/frontend/providers/VeltProvider.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\nfunction useVeltCollaboration() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(VeltContext);\n    if (context === undefined) {\n        throw new Error(\"useVeltCollaboration must be used within a VeltCollaborationProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./providers/VeltProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"631c0d0d2feb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWFsZGFzaC1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz8xNDAyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNjMxYzBkMGQyZmViXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _veltdev_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @veltdev/react */ \"(rsc)/./node_modules/.pnpm/@veltdev+react@1.0.164_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@veltdev/react/esm/index.js\");\n/* harmony import */ var _providers_QueryProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/QueryProvider */ \"(rsc)/./providers/QueryProvider.tsx\");\n/* harmony import */ var _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/AuthProvider */ \"(rsc)/./providers/AuthProvider.tsx\");\n/* harmony import */ var _providers_VeltProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/VeltProvider */ \"(rsc)/./providers/VeltProvider.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"DealDash - Powersports Dealership Platform\",\n    description: \"AI-powered analytics and management platform for powersports dealerships\",\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#3b82f6\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className)} h-full bg-gray-50`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_3__.QueryProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_veltdev_react__WEBPACK_IMPORTED_MODULE_2__.VeltProvider, {\n                        apiKey: \"yuXI1Qabg64728wbcZHh\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_VeltProvider__WEBPACK_IMPORTED_MODULE_5__.VeltCollaborationProvider, {\n                            children: [\n                                children,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.Toaster, {\n                                    position: \"top-right\",\n                                    toastOptions: {\n                                        duration: 4000,\n                                        style: {\n                                            background: \"#363636\",\n                                            color: \"#fff\"\n                                        },\n                                        success: {\n                                            duration: 3000,\n                                            iconTheme: {\n                                                primary: \"#10b981\",\n                                                secondary: \"#fff\"\n                                            }\n                                        },\n                                        error: {\n                                            duration: 5000,\n                                            iconTheme: {\n                                                primary: \"#ef4444\",\n                                                secondary: \"#fff\"\n                                            }\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/learning/python/dealdash/frontend/app/layout.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/learning/python/dealdash/frontend/app/layout.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/learning/python/dealdash/frontend/app/layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/learning/python/dealdash/frontend/app/layout.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/learning/python/dealdash/frontend/app/layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/learning/python/dealdash/frontend/app/layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/learning/python/dealdash/frontend/app/layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./providers/AuthProvider.tsx":
/*!************************************!*\
  !*** ./providers/AuthProvider.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/learning/python/dealdash/frontend/providers/AuthProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/learning/python/dealdash/frontend/providers/AuthProvider.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/learning/python/dealdash/frontend/providers/AuthProvider.tsx#useAuth`);


/***/ }),

/***/ "(rsc)/./providers/QueryProvider.tsx":
/*!*************************************!*\
  !*** ./providers/QueryProvider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/learning/python/dealdash/frontend/providers/QueryProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/learning/python/dealdash/frontend/providers/QueryProvider.tsx#QueryProvider`);


/***/ }),

/***/ "(rsc)/./providers/VeltProvider.tsx":
/*!************************************!*\
  !*** ./providers/VeltProvider.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   VeltCollaborationProvider: () => (/* binding */ e0),
/* harmony export */   useVeltCollaboration: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/learning/python/dealdash/frontend/providers/VeltProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/learning/python/dealdash/frontend/providers/VeltProvider.tsx#VeltCollaborationProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/learning/python/dealdash/frontend/providers/VeltProvider.tsx#useVeltCollaboration`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/@swc+helpers@0.5.2","vendor-chunks/@tanstack+query-devtools@5.80.0","vendor-chunks/@workos-inc+node@7.21.0_next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_","vendor-chunks/jose@5.10.0","vendor-chunks/jose@5.6.3","vendor-chunks/axios@1.10.0","vendor-chunks/@tanstack+query-core@5.80.7","vendor-chunks/@peculiar+asn1-schema@2.3.15","vendor-chunks/@workos-inc+authkit-nextjs@0.10.1_next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3_1bb817457dba2a05273873c5a29d727e","vendor-chunks/asynckit@0.4.0","vendor-chunks/@tanstack+react-query@5.80.7_react@18.3.1","vendor-chunks/math-intrinsics@1.1.0","vendor-chunks/es-errors@1.3.0","vendor-chunks/call-bind-apply-helpers@1.0.2","vendor-chunks/debug@4.4.1","vendor-chunks/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@18.3.1__react@18.3.1","vendor-chunks/get-proto@1.0.1","vendor-chunks/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/mime-db@1.52.0","vendor-chunks/has-symbols@1.1.0","vendor-chunks/gopd@1.2.0","vendor-chunks/function-bind@1.1.2","vendor-chunks/form-data@4.0.3","vendor-chunks/follow-redirects@1.15.9","vendor-chunks/@veltdev+react@1.0.164_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/tslib@2.8.1","vendor-chunks/js-cookie@3.0.5","vendor-chunks/goober@2.1.16_csstype@3.1.3","vendor-chunks/uncrypto@0.1.3","vendor-chunks/iron-webcrypto@1.2.1","vendor-chunks/iron-session@8.0.4","vendor-chunks/webcrypto-core@1.8.1","vendor-chunks/supports-color@7.2.0","vendor-chunks/pvutils@1.1.3","vendor-chunks/pvtsutils@1.3.6","vendor-chunks/proxy-from-env@1.1.0","vendor-chunks/pluralize@8.0.0","vendor-chunks/path-to-regexp@6.3.0","vendor-chunks/ms@2.1.3","vendor-chunks/mime-types@2.1.35","vendor-chunks/iron-webcrypto@0.2.8","vendor-chunks/iron-session@6.3.1_next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_","vendor-chunks/ieee754@1.2.1","vendor-chunks/hasown@2.0.2","vendor-chunks/has-tostringtag@1.0.2","vendor-chunks/has-flag@4.0.0","vendor-chunks/get-intrinsic@1.3.0","vendor-chunks/es-set-tostringtag@2.1.0","vendor-chunks/es-object-atoms@1.1.1","vendor-chunks/es-define-property@1.0.1","vendor-chunks/dunder-proto@1.0.1","vendor-chunks/delayed-stream@1.0.0","vendor-chunks/cookie@0.7.2","vendor-chunks/cookie@0.5.0","vendor-chunks/combined-stream@1.0.8","vendor-chunks/buffer@6.0.3","vendor-chunks/base64-js@1.5.1","vendor-chunks/asn1js@3.0.6","vendor-chunks/@peculiar+webcrypto@1.5.0","vendor-chunks/@peculiar+json-schema@1.1.12"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.0.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2F.pnpm%2Fnext%4014.0.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdaniel%2Flearning%2Fpython%2Fdealdash%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();