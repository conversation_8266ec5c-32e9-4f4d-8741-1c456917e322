# Velt.dev Integration Guide for DealDash

## Overview
Integrate Velt.dev for real-time collaboration features including presence, comments, cursors, and notifications across the DealDash platform.

## Setup & Installation

### 1. Install Velt SDK
```bash
npm install @veltdev/react
```

### 2. Environment Configuration
```env
# .env.local
NEXT_PUBLIC_VELT_API_KEY=your_velt_api_key
VELT_AUTH_TOKEN=your_velt_auth_token
```

### 3. Velt Provider Setup
```typescript
// app/layout.tsx
import { VeltProvider } from '@veltdev/react';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <VeltProvider apiKey={process.env.NEXT_PUBLIC_VELT_API_KEY}>
          {children}
        </VeltProvider>
      </body>
    </html>
  );
}
```

## User Authentication Integration

### 1. Sync Users with Velt
```typescript
// hooks/useVeltAuth.ts
import { useVeltClient } from '@veltdev/react';
import { useUser } from '@/hooks/useAuth';

export const useVeltAuth = () => {
  const { user } = useUser();
  const { client } = useVeltClient();

  useEffect(() => {
    if (user && client) {
      // Identify user with Velt
      client.identify({
        userId: user.id.toString(),
        name: user.full_name,
        email: user.email,
        avatar: user.avatar_url,
        organizationId: user.current_dealership_id?.toString(),
        groupId: `dealership_${user.current_dealership_id}`,
      });
    }
  }, [user, client]);
};
```

### 2. Document Context Setup
```typescript
// hooks/useVeltDocument.ts
import { useVeltClient } from '@veltdev/react';

export const useVeltDocument = (documentId: string, dealershipId: number) => {
  const { client } = useVeltClient();

  useEffect(() => {
    if (client) {
      // Set document context for collaboration
      client.setDocument({
        documentId,
        organizationId: dealershipId.toString(),
        metadata: {
          dealershipId,
          documentType: 'dashboard',
        }
      });
    }
  }, [documentId, dealershipId, client]);
};
```

## Collaboration Features Implementation

### 1. Real-time Presence
```typescript
// components/PresenceIndicator.tsx
import { VeltPresence } from '@veltdev/react';

export const PresenceIndicator = ({ location }: { location: string }) => {
  return (
    <div className="flex items-center space-x-2">
      <VeltPresence 
        location={location}
        maxUsers={5}
        showUserNames={true}
      />
    </div>
  );
};

// Usage in dashboard
<PresenceIndicator location="inventory_dashboard" />
```

### 2. Contextual Comments
```typescript
// components/CommentableCard.tsx
import { VeltComments } from '@veltdev/react';

interface CommentableCardProps {
  id: string;
  type: 'inventory' | 'sale' | 'insight';
  children: React.ReactNode;
}

export const CommentableCard = ({ id, type, children }: CommentableCardProps) => {
  return (
    <div className="relative">
      <VeltComments 
        commentElement={id}
        metadata={{
          type,
          id,
          timestamp: new Date().toISOString()
        }}
      />
      {children}
    </div>
  );
};

// Usage
<CommentableCard id={`inventory_${item.id}`} type="inventory">
  <InventoryCard item={item} />
</CommentableCard>
```

### 3. Live Cursors
```typescript
// components/LiveCursors.tsx
import { VeltCursor } from '@veltdev/react';

export const LiveCursors = () => {
  return (
    <VeltCursor 
      cursorName="default"
      showCursorNames={true}
    />
  );
};

// Add to main layout
<div className="relative">
  <LiveCursors />
  <DashboardContent />
</div>
```

### 4. Notifications
```typescript
// components/NotificationCenter.tsx
import { VeltNotifications } from '@veltdev/react';

export const NotificationCenter = () => {
  return (
    <div className="relative">
      <VeltNotifications 
        position="top-right"
        maxNotifications={5}
        autoHide={true}
        hideAfter={5000}
      />
    </div>
  );
};
```

## Dashboard-Specific Integrations

### 1. Inventory Management
```typescript
// pages/inventory/index.tsx
import { useVeltAuth, useVeltDocument } from '@/hooks/velt';
import { PresenceIndicator, CommentableCard } from '@/components/collaboration';

export default function InventoryPage() {
  const { user } = useUser();
  
  useVeltAuth();
  useVeltDocument('inventory_dashboard', user.current_dealership_id);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1>Inventory Management</h1>
        <PresenceIndicator location="inventory_page" />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {inventoryItems.map((item) => (
          <CommentableCard 
            key={item.id}
            id={`inventory_${item.id}`}
            type="inventory"
          >
            <InventoryCard item={item} />
          </CommentableCard>
        ))}
      </div>
    </div>
  );
}
```

### 2. Sales Analytics
```typescript
// components/SalesChart.tsx
import { VeltComments } from '@veltdev/react';
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer } from 'recharts';

export const SalesChart = ({ data, chartId }: { data: any[], chartId: string }) => {
  return (
    <div className="relative">
      <VeltComments 
        commentElement={chartId}
        metadata={{
          type: 'chart',
          chartType: 'sales_trend',
          dataPoints: data.length
        }}
      />
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={data}>
          <XAxis dataKey="date" />
          <YAxis />
          <Line type="monotone" dataKey="sales" stroke="#8884d8" />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};
```

### 3. AI Insights Collaboration
```typescript
// components/AIInsightCard.tsx
import { VeltComments, VeltReactions } from '@veltdev/react';

export const AIInsightCard = ({ insight }: { insight: AIInsight }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <VeltComments 
        commentElement={`insight_${insight.id}`}
        metadata={{
          type: 'ai_insight',
          confidence: insight.confidence,
          generated_at: insight.generated_at
        }}
      />
      
      <div className="flex justify-between items-start mb-4">
        <h3 className="text-lg font-semibold">{insight.title}</h3>
        <VeltReactions 
          elementId={`insight_${insight.id}`}
          reactions={['👍', '👎', '🤔', '💡']}
        />
      </div>
      
      <p className="text-gray-600 mb-4">{insight.description}</p>
      
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-500">
          Confidence: {insight.confidence}%
        </span>
        <button className="text-blue-600 hover:text-blue-800">
          View Details
        </button>
      </div>
    </div>
  );
};
```

## Advanced Features

### 1. Team Workspaces
```typescript
// hooks/useWorkspaceCollaboration.ts
export const useWorkspaceCollaboration = (workspaceId: string) => {
  const { client } = useVeltClient();

  useEffect(() => {
    if (client) {
      // Set workspace-specific collaboration context
      client.setDocument({
        documentId: `workspace_${workspaceId}`,
        metadata: {
          workspaceId,
          type: 'workspace'
        }
      });
    }
  }, [workspaceId, client]);
};
```

### 2. Comment Threading
```typescript
// components/ThreadedComments.tsx
import { VeltComments } from '@veltdev/react';

export const ThreadedComments = ({ elementId, allowReplies = true }) => {
  return (
    <VeltComments 
      commentElement={elementId}
      allowReplies={allowReplies}
      showCommentStats={true}
      priority="high"
      metadata={{
        allowThreading: true,
        maxDepth: 3
      }}
    />
  );
};
```

### 3. Collaborative Filtering
```typescript
// components/CollaborativeFilters.tsx
import { VeltLiveState } from '@veltdev/react';

export const CollaborativeFilters = () => {
  const [filters, setFilters] = VeltLiveState('dashboard_filters', {
    dateRange: 'last_30_days',
    category: 'all',
    status: 'active'
  });

  return (
    <div className="flex space-x-4">
      <select 
        value={filters.dateRange}
        onChange={(e) => setFilters({...filters, dateRange: e.target.value})}
      >
        <option value="last_7_days">Last 7 Days</option>
        <option value="last_30_days">Last 30 Days</option>
        <option value="last_90_days">Last 90 Days</option>
      </select>
      
      <select 
        value={filters.category}
        onChange={(e) => setFilters({...filters, category: e.target.value})}
      >
        <option value="all">All Categories</option>
        <option value="atv">ATVs</option>
        <option value="motorcycle">Motorcycles</option>
      </select>
    </div>
  );
};
```

## Security & Privacy

### 1. Multi-tenant Isolation
```typescript
// Ensure dealership data isolation
const documentId = `dealership_${dealershipId}_${pageType}_${resourceId}`;
const organizationId = dealershipId.toString();
```

### 2. Role-based Permissions
```typescript
// Set user permissions based on dealership role
client.identify({
  userId: user.id.toString(),
  name: user.full_name,
  email: user.email,
  organizationId: dealershipId.toString(),
  metadata: {
    role: user.dealership_role, // owner, manager, sales, viewer
    permissions: user.permissions
  }
});
```

## Performance Optimization

### 1. Lazy Loading
```typescript
// Lazy load collaboration features
const VeltComments = lazy(() => import('@veltdev/react').then(module => ({ 
  default: module.VeltComments 
})));

// Use with Suspense
<Suspense fallback={<div>Loading comments...</div>}>
  <VeltComments commentElement={elementId} />
</Suspense>
```

### 2. Selective Feature Loading
```typescript
// Load only needed features per page
const collaborationFeatures = {
  inventory: ['comments', 'presence'],
  sales: ['comments', 'presence', 'cursors'],
  analytics: ['comments', 'reactions'],
  reports: ['comments', 'presence']
};
```

## Monitoring & Analytics

### 1. Collaboration Metrics
```typescript
// Track collaboration usage
const trackCollaborationEvent = (event: string, metadata: any) => {
  analytics.track('collaboration_event', {
    event,
    dealership_id: user.current_dealership_id,
    user_id: user.id,
    ...metadata
  });
};

// Usage
<VeltComments 
  commentElement={elementId}
  onCommentAdd={() => trackCollaborationEvent('comment_added', { elementId })}
  onCommentReply={() => trackCollaborationEvent('comment_replied', { elementId })}
/>
```

## Best Practices

### 1. Element ID Naming Convention
```typescript
// Consistent naming for collaboration elements
const generateElementId = (type: string, id: string | number, context?: string) => {
  const base = `${type}_${id}`;
  return context ? `${base}_${context}` : base;
};

// Examples:
// generateElementId('inventory', 123) -> 'inventory_123'
// generateElementId('sale', 456, 'details') -> 'sale_456_details'
// generateElementId('insight', 789, 'chart') -> 'insight_789_chart'
```

### 2. Context-Aware Collaboration
```typescript
// Provide rich context for better collaboration
const commentMetadata = {
  type: 'inventory_item',
  sku: item.sku,
  name: item.name,
  category: item.category,
  current_stock: item.quantity_on_hand,
  last_updated: item.updated_at,
  dealership_id: dealershipId
};
```

This integration will give you enterprise-grade collaboration features with minimal development effort, allowing your dealership teams to collaborate effectively on inventory management, sales analysis, and strategic planning!
