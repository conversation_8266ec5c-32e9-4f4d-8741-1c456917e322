# Core FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database and ORM
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.12.1

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Redis and caching
redis==5.0.1
aioredis==2.0.1

# Background tasks
celery==5.3.4
flower==2.0.1

# Data processing and AI
pandas==2.1.3
numpy==1.25.2
scikit-learn==1.3.2
openai==1.3.7

# HTTP client for API integrations
httpx==0.25.2
aiohttp==3.9.1

# Configuration and environment
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# Logging and monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8
