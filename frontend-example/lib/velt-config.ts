// Velt Configuration for DealDash
import { VeltConfig } from '@veltdev/react';

export const veltConfig: VeltConfig = {
  // API Configuration
  apiKey: process.env.NEXT_PUBLIC_VELT_API_KEY!,
  
  // Feature Configuration
  features: {
    comments: {
      enabled: true,
      allowReplies: true,
      allowReactions: true,
      allowMentions: true,
      showCommentStats: true,
      priority: 'high',
      moderationMode: 'auto', // auto, manual, none
    },
    
    presence: {
      enabled: true,
      showUserNames: true,
      showUserAvatars: true,
      maxUsers: 10,
      timeout: 30000, // 30 seconds
    },
    
    cursors: {
      enabled: true,
      showCursorNames: true,
      showCursorChat: false,
      cursorTimeout: 5000, // 5 seconds
    },
    
    notifications: {
      enabled: true,
      position: 'top-right',
      maxNotifications: 5,
      autoHide: true,
      hideAfter: 5000,
      sound: true,
    },
    
    liveState: {
      enabled: true,
      syncInterval: 1000, // 1 second
    },
    
    reactions: {
      enabled: true,
      allowCustomReactions: false,
      defaultReactions: ['👍', '👎', '❤️', '😊', '🤔', '💡'],
    }
  },
  
  // UI Customization
  theme: {
    primary: '#3B82F6', // Blue-600
    secondary: '#6B7280', // Gray-500
    success: '#10B981', // Green-500
    warning: '#F59E0B', // Yellow-500
    error: '#EF4444', // Red-500
    background: '#FFFFFF',
    surface: '#F9FAFB', // Gray-50
    text: '#111827', // Gray-900
    textSecondary: '#6B7280', // Gray-500
  },
  
  // Security & Privacy
  security: {
    enableEncryption: true,
    dataRetention: 90, // days
    allowAnonymous: false,
    requireEmailVerification: true,
  },
  
  // Performance
  performance: {
    enableCaching: true,
    cacheTimeout: 300000, // 5 minutes
    enableCompression: true,
    batchUpdates: true,
    maxConcurrentConnections: 100,
  }
};

// Dealership-specific configuration
export const getDealershipVeltConfig = (dealershipId: number) => ({
  ...veltConfig,
  organizationId: dealershipId.toString(),
  metadata: {
    dealershipId,
    environment: process.env.NODE_ENV,
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
  }
});

// Document types for different pages
export const documentTypes = {
  DASHBOARD: 'dashboard',
  INVENTORY: 'inventory',
  SALES: 'sales',
  ANALYTICS: 'analytics',
  REPORTS: 'reports',
  SETTINGS: 'settings',
} as const;

// Generate document ID for collaboration context
export const generateDocumentId = (
  type: keyof typeof documentTypes,
  dealershipId: number,
  resourceId?: string | number
) => {
  const baseId = `${documentTypes[type]}_${dealershipId}`;
  return resourceId ? `${baseId}_${resourceId}` : baseId;
};

// Generate element ID for comments/collaboration
export const generateElementId = (
  type: string,
  id: string | number,
  context?: string
) => {
  const baseId = `${type}_${id}`;
  return context ? `${baseId}_${context}` : baseId;
};

// User role mapping for Velt permissions
export const mapUserRoleToVeltPermissions = (role: string) => {
  const rolePermissions = {
    owner: {
      canComment: true,
      canReply: true,
      canMention: true,
      canReact: true,
      canModerate: true,
      canDelete: true,
      canEdit: true,
    },
    manager: {
      canComment: true,
      canReply: true,
      canMention: true,
      canReact: true,
      canModerate: true,
      canDelete: false,
      canEdit: true,
    },
    sales: {
      canComment: true,
      canReply: true,
      canMention: true,
      canReact: true,
      canModerate: false,
      canDelete: false,
      canEdit: false,
    },
    viewer: {
      canComment: false,
      canReply: false,
      canMention: false,
      canReact: true,
      canModerate: false,
      canDelete: false,
      canEdit: false,
    },
  };
  
  return rolePermissions[role as keyof typeof rolePermissions] || rolePermissions.viewer;
};

// Location mapping for presence tracking
export const presenceLocations = {
  MAIN_DASHBOARD: 'main_dashboard',
  INVENTORY_PAGE: 'inventory_page',
  INVENTORY_ITEM: 'inventory_item',
  SALES_PAGE: 'sales_page',
  SALES_DETAIL: 'sales_detail',
  ANALYTICS_PAGE: 'analytics_page',
  REPORTS_PAGE: 'reports_page',
  SETTINGS_PAGE: 'settings_page',
} as const;

// Comment metadata templates
export const commentMetadataTemplates = {
  inventoryItem: (item: any) => ({
    type: 'inventory_item',
    sku: item.sku,
    name: item.name,
    category: item.category,
    currentStock: item.quantity_on_hand,
    price: item.retail_price,
    isLowStock: item.is_low_stock,
  }),
  
  sale: (sale: any) => ({
    type: 'sale',
    saleNumber: sale.sale_number,
    customerName: sale.customer_name,
    totalAmount: sale.total_amount,
    saleDate: sale.sale_date,
    status: sale.status,
  }),
  
  insight: (insight: any) => ({
    type: 'ai_insight',
    confidence: insight.confidence,
    generatedAt: insight.generated_at,
    insightType: insight.type,
  }),
  
  chart: (chartData: any) => ({
    type: 'chart',
    chartType: chartData.type,
    dataPoints: chartData.dataPoints,
    timeRange: chartData.timeRange,
  }),
};

// Notification templates
export const notificationTemplates = {
  lowStock: (item: any) => ({
    title: 'Low Stock Alert',
    message: `${item.name} (${item.sku}) is running low with only ${item.quantity_on_hand} units remaining.`,
    type: 'warning',
    actionUrl: `/inventory/${item.id}`,
  }),
  
  newComment: (comment: any, user: any) => ({
    title: 'New Comment',
    message: `${user.name} commented on ${comment.targetType}`,
    type: 'info',
    actionUrl: comment.actionUrl,
  }),
  
  mention: (mention: any, user: any) => ({
    title: 'You were mentioned',
    message: `${user.name} mentioned you in a comment`,
    type: 'info',
    actionUrl: mention.actionUrl,
  }),
};

// Export default configuration
export default veltConfig;
