import React, { useEffect, useState } from 'react';
import { VeltProvider, VeltComments, VeltPresence, VeltCursor, VeltNotifications } from '@veltdev/react';
import { useUser } from '@/hooks/useAuth';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MessageCircle, Users, TrendingUp, Package, DollarSign, AlertTriangle } from 'lucide-react';

// Types
interface InventoryItem {
  id: number;
  sku: string;
  name: string;
  brand: string;
  category: string;
  quantity_on_hand: number;
  retail_price: number;
  margin: number;
  is_low_stock: boolean;
  ai_insights?: {
    stock_status: string;
    reorder_recommendation: string;
    confidence: string;
  };
}

interface SaleMetrics {
  total_sales_30d: number;
  total_revenue_30d: number;
  avg_sale_amount: number;
  top_selling_items: Array<{
    name: string;
    quantity_sold: number;
    revenue: number;
  }>;
}

// Collaboration-enabled Card Component
const CollaborativeCard: React.FC<{
  id: string;
  type: string;
  children: React.ReactNode;
  className?: string;
}> = ({ id, type, children, className = '' }) => {
  return (
    <div className={`relative ${className}`}>
      <VeltComments 
        commentElement={id}
        metadata={{
          type,
          timestamp: new Date().toISOString()
        }}
      />
      {children}
    </div>
  );
};

// Inventory Item Card with Collaboration
const InventoryItemCard: React.FC<{ item: InventoryItem }> = ({ item }) => {
  const elementId = `inventory_${item.id}`;
  
  return (
    <CollaborativeCard id={elementId} type="inventory_item">
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg">{item.name}</CardTitle>
              <p className="text-sm text-gray-600">{item.brand} • {item.sku}</p>
            </div>
            <Badge variant={item.is_low_stock ? "destructive" : "secondary"}>
              {item.quantity_on_hand} in stock
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Price:</span>
              <span className="font-semibold">${item.retail_price.toLocaleString()}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Margin:</span>
              <span className={`font-semibold ${item.margin > 30 ? 'text-green-600' : 'text-yellow-600'}`}>
                {item.margin.toFixed(1)}%
              </span>
            </div>
            
            {item.ai_insights && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <h4 className="text-sm font-semibold text-blue-800 mb-2">AI Insights</h4>
                <p className="text-sm text-blue-700">{item.ai_insights.reorder_recommendation}</p>
                <Badge variant="outline" className="mt-2 text-xs">
                  {item.ai_insights.confidence} confidence
                </Badge>
              </div>
            )}
            
            {item.is_low_stock && (
              <div className="flex items-center space-x-2 text-red-600">
                <AlertTriangle size={16} />
                <span className="text-sm font-medium">Low Stock Alert</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </CollaborativeCard>
  );
};

// Sales Metrics Card with Collaboration
const SalesMetricsCard: React.FC<{ metrics: SaleMetrics }> = ({ metrics }) => {
  const elementId = "sales_metrics_overview";
  
  return (
    <CollaborativeCard id={elementId} type="sales_metrics">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Sales Performance (30 Days)</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {metrics.total_sales_30d}
              </div>
              <div className="text-sm text-gray-600">Total Sales</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                ${metrics.total_revenue_30d.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Revenue</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                ${metrics.avg_sale_amount.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Avg Sale</div>
            </div>
          </div>
          
          <div className="mt-6">
            <h4 className="font-semibold mb-3">Top Selling Items</h4>
            <div className="space-y-2">
              {metrics.top_selling_items.map((item, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm">{item.name}</span>
                  <div className="text-right">
                    <div className="text-sm font-semibold">{item.quantity_sold} sold</div>
                    <div className="text-xs text-gray-600">${item.revenue.toLocaleString()}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </CollaborativeCard>
  );
};

// Main Dashboard Component
const DashboardWithCollaboration: React.FC<{
  dealershipId: number;
  inventoryItems: InventoryItem[];
  salesMetrics: SaleMetrics;
}> = ({ dealershipId, inventoryItems, salesMetrics }) => {
  const { user } = useUser();
  
  // Initialize Velt collaboration
  useEffect(() => {
    if (user && window.Velt) {
      // Identify user with Velt
      window.Velt.identify({
        userId: user.id.toString(),
        name: user.full_name,
        email: user.email,
        avatar: user.avatar_url,
        organizationId: dealershipId.toString(),
        groupId: `dealership_${dealershipId}`,
      });
      
      // Set document context
      window.Velt.setDocument({
        documentId: `dashboard_${dealershipId}`,
        organizationId: dealershipId.toString(),
        metadata: {
          dealershipId,
          documentType: 'main_dashboard',
        }
      });
    }
  }, [user, dealershipId]);
  
  const lowStockItems = inventoryItems.filter(item => item.is_low_stock);
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Live Cursors */}
      <VeltCursor 
        cursorName="default"
        showCursorNames={true}
      />
      
      {/* Notifications */}
      <VeltNotifications 
        position="top-right"
        maxNotifications={5}
        autoHide={true}
        hideAfter={5000}
      />
      
      {/* Header with Presence */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">DealDash Dashboard</h1>
              <p className="text-gray-600">Real-time insights for your dealership</p>
            </div>
            
            <div className="flex items-center space-x-4">
              <VeltPresence 
                location="main_dashboard"
                maxUsers={8}
                showUserNames={true}
              />
              <Button variant="outline" size="sm">
                <MessageCircle className="h-4 w-4 mr-2" />
                Comments
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <CollaborativeCard id="stat_total_inventory" type="stat">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Package className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold">{inventoryItems.length}</div>
                    <div className="text-sm text-gray-600">Total Items</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </CollaborativeCard>
          
          <CollaborativeCard id="stat_low_stock" type="stat">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold">{lowStockItems.length}</div>
                    <div className="text-sm text-gray-600">Low Stock</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </CollaborativeCard>
          
          <CollaborativeCard id="stat_revenue" type="stat">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <DollarSign className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold">
                      ${salesMetrics.total_revenue_30d.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">30d Revenue</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </CollaborativeCard>
          
          <CollaborativeCard id="stat_team_activity" type="stat">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold">
                      <VeltPresence 
                        location="main_dashboard"
                        maxUsers={1}
                        showUserNames={false}
                        showCount={true}
                      />
                    </div>
                    <div className="text-sm text-gray-600">Active Users</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </CollaborativeCard>
        </div>
        
        {/* Sales Metrics */}
        <div className="mb-8">
          <SalesMetricsCard metrics={salesMetrics} />
        </div>
        
        {/* Inventory Grid */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold">Inventory Overview</h2>
            <VeltPresence 
              location="inventory_section"
              maxUsers={5}
              showUserNames={true}
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {inventoryItems.slice(0, 6).map((item) => (
              <InventoryItemCard key={item.id} item={item} />
            ))}
          </div>
        </div>
        
        {/* Low Stock Alerts */}
        {lowStockItems.length > 0 && (
          <CollaborativeCard id="low_stock_alerts" type="alert_section">
            <Card className="border-red-200 bg-red-50">
              <CardHeader>
                <CardTitle className="text-red-800 flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5" />
                  <span>Low Stock Alerts</span>
                </CardTitle>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  {lowStockItems.map((item) => (
                    <div key={item.id} className="flex justify-between items-center p-3 bg-white rounded-lg">
                      <div>
                        <div className="font-semibold">{item.name}</div>
                        <div className="text-sm text-gray-600">{item.sku}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-red-600 font-semibold">{item.quantity_on_hand} left</div>
                        <Button size="sm" variant="outline" className="mt-1">
                          Reorder
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </CollaborativeCard>
        )}
      </div>
    </div>
  );
};

export default DashboardWithCollaboration;
