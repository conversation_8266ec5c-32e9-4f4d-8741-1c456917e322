# DealDash Platform

A scalable platform for powersports dealerships with Lightspeed integration and AI-powered insights.

## Features

- **Multi-tenant Architecture**: Support for multiple dealerships with role-based access control
- **Lightspeed Integration**: Automated data synchronization with Lightspeed Retail
- **AI-Powered Insights**: Inventory optimization, sales forecasting, and customer analytics
- **Real-time Dashboard**: Interactive web dashboard with data visualization
- **Background Processing**: Automated data sync and AI processing with Celery
- **Scalable Infrastructure**: Docker-based deployment with Redis caching

## Technology Stack

### Backend
- **FastAPI**: Modern, fast web framework with automatic API documentation
- **SQLAlchemy**: Async ORM with PostgreSQL
- **Redis**: Caching and message broker
- **Celery**: Background task processing
- **Pydantic**: Data validation and serialization

### AI & Analytics
- **OpenAI API**: GPT models for insights and recommendations
- **Pandas**: Data processing and analysis
- **Scikit-learn**: Machine learning for predictive analytics

### Infrastructure
- **Docker**: Containerization
- **PostgreSQL**: Primary database
- **Alembic**: Database migrations
- **Prometheus**: Monitoring (planned)

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Python 3.11+ (for local development)

### Environment Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd dealdash
```

2. Copy environment file:
```bash
cp .env.example .env
```

3. Update the `.env` file with your configuration:
   - Set strong passwords for database and Redis
   - Add your Lightspeed API credentials
   - Add your OpenAI API key

### Running with Docker

1. Start all services:
```bash
docker-compose up -d
```

2. Run database migrations:
```bash
docker-compose exec api alembic upgrade head
```

3. Access the application:
   - API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - Flower (Celery monitoring): http://localhost:5555

### Local Development

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Start PostgreSQL and Redis:
```bash
docker-compose up -d postgres redis
```

3. Run database migrations:
```bash
alembic upgrade head
```

4. Start the FastAPI server:
```bash
uvicorn app.main:app --reload
```

5. Start Celery worker (in another terminal):
```bash
celery -A app.core.celery worker --loglevel=info
```

6. Start Celery beat (in another terminal):
```bash
celery -A app.core.celery beat --loglevel=info
```

## API Documentation

Once the application is running, you can access:
- Interactive API docs: http://localhost:8000/docs
- ReDoc documentation: http://localhost:8000/redoc

## Database Migrations

Create a new migration:
```bash
alembic revision --autogenerate -m "Description of changes"
```

Apply migrations:
```bash
alembic upgrade head
```

## Testing

Run tests:
```bash
pytest
```

Run tests with coverage:
```bash
pytest --cov=app
```

## Project Structure

```
dealdash/
├── app/
│   ├── api/                 # API routes
│   ├── core/               # Core configuration and utilities
│   ├── models/             # SQLAlchemy models
│   ├── schemas/            # Pydantic schemas
│   ├── services/           # Business logic
│   └── tasks/              # Celery tasks
├── alembic/                # Database migrations
├── tests/                  # Test files
├── docker-compose.yml      # Docker services
├── Dockerfile             # Application container
└── requirements.txt       # Python dependencies
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

[License information]
