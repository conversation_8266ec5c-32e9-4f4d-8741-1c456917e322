from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel


class PipelineMetrics(BaseModel):
    records_processed: int
    records_enriched: int
    insights_generated: int
    errors: int
    execution_time: float


class ProcessingResponse(BaseModel):
    message: str
    task_id: str
    dealership_id: int


class InsightResponse(BaseModel):
    has_insights: bool
    dealership_id: int
    insights: Optional[Dict[str, Any]] = None
    message: Optional[str] = None


class InventoryInsight(BaseModel):
    id: int
    sku: str
    name: str
    brand: Optional[str]
    category: Optional[str]
    quantity_on_hand: int
    quantity_available: int
    retail_price: Optional[float]
    margin: float
    is_low_stock: bool
    ai_insights: Dict[str, Any]


class InventoryInsightsResponse(BaseModel):
    items: List[InventoryInsight]
    total: int
    page: int
    page_size: int
    has_more: bool


class SalesInsight(BaseModel):
    id: int
    sale_number: str
    sale_date: str
    total_amount: float
    item_count: int
    customer_name: Optional[str]
    payment_method: Optional[str]
    status: str
    ai_insights: Dict[str, Any]


class SalesInsightsResponse(BaseModel):
    sales: List[SalesInsight]
    total: int
    page: int
    page_size: int
    days: int
    has_more: bool


class TaskInfo(BaseModel):
    type: str
    task_id: str


class InsightGenerationResponse(BaseModel):
    message: str
    dealership_id: int
    tasks: List[TaskInfo]


class InventoryStats(BaseModel):
    total_items: int
    total_value: float
    low_stock_count: int


class SalesStats(BaseModel):
    total_sales: int
    total_revenue: float
    avg_sale_amount: float


class DashboardData(BaseModel):
    dealership_id: int
    last_updated: str
    inventory: InventoryStats
    sales_30d: SalesStats
    insights: List[Dict[str, Any]]
    has_cached_insights: bool


class AIInsightBase(BaseModel):
    generated_at: str
    confidence: str
    ai_model: str


class InventoryAIInsight(AIInsightBase):
    sku: str
    stock_status: str
    reorder_recommendation: str
    pricing_suggestion: str
    velocity_insight: str
    risk_assessment: str


class SalesAIInsight(AIInsightBase):
    sale_id: int
    performance_assessment: str
    customer_insight: str
    cross_sell_opportunities: List[str]
    seasonal_notes: str
    follow_up_actions: List[str]


class DemandForecast(AIInsightBase):
    sku: str
    forecast_30d: int
    forecast_90d: int
    seasonal_factors: Dict[str, float]
    confidence_level: str
    safety_stock_recommendation: int
    forecast_type: str


class CustomerInsight(AIInsightBase):
    customer_name: str
    segment: str
    behavior_pattern: str
    product_recommendations: List[str]
    retention_strategy: str
    lifetime_value_estimate: float


class PricingRecommendation(AIInsightBase):
    sku: str
    current_price: float
    optimal_price: float
    elasticity_assessment: str
    competitive_position: str
    margin_strategy: str
    dynamic_pricing: str


class CrossFunctionalInsight(BaseModel):
    type: str
    data: Dict[str, Any]
    generated_at: str


class InventoryTurnoverInsight(BaseModel):
    top_performers: Dict[str, Dict[str, Any]]
    slow_movers: Dict[str, Dict[str, Any]]
    avg_turnover_rate: float


class SeasonalTrendsInsight(BaseModel):
    monthly_breakdown: Dict[str, Dict[str, Any]]
    peak_season: Dict[str, Any]
    low_season: Dict[str, Any]


class CustomerPatternsInsight(BaseModel):
    total_customers: int
    repeat_customers: int
    high_value_customers: int
    avg_purchases_per_customer: float
    avg_customer_value: float


class ProfitabilityInsight(BaseModel):
    overall_profit_margin: float
    total_profit: float
    most_profitable_products: Dict[str, Dict[str, Any]]
    least_profitable_products: Dict[str, Dict[str, Any]]


class DataQualityMetrics(BaseModel):
    completeness_score: float
    accuracy_score: float
    consistency_score: float
    timeliness_score: float
    overall_score: float
    issues: List[str]
    recommendations: List[str]


class PipelineStatus(BaseModel):
    is_running: bool
    last_run: Optional[str]
    next_scheduled_run: Optional[str]
    status: str
    error_message: Optional[str]
    metrics: Optional[PipelineMetrics]


class RecommendationEngine(BaseModel):
    type: str
    priority: str
    title: str
    description: str
    action_items: List[str]
    expected_impact: str
    confidence_score: float
    generated_at: str
