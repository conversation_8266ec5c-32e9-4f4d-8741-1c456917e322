from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, EmailStr, validator
from enum import Enum

from app.models.dealership import UserRole


class DealershipBase(BaseModel):
    name: str
    business_name: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    country: str = "US"
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    website: Optional[str] = None
    tax_id: Optional[str] = None
    license_number: Optional[str] = None


class DealershipCreate(DealershipBase):
    pass


class DealershipUpdate(BaseModel):
    name: Optional[str] = None
    business_name: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    country: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    website: Optional[str] = None
    tax_id: Optional[str] = None
    license_number: Optional[str] = None
    is_active: Optional[bool] = None
    subscription_tier: Optional[str] = None


class DealershipInDBBase(DealershipBase):
    id: int
    is_active: bool
    subscription_tier: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class Dealership(DealershipInDBBase):
    """Dealership schema for API responses"""
    pass


class DealershipWithUsers(DealershipInDBBase):
    """Dealership schema with user associations"""
    user_count: int
    lightspeed_connected: bool = False


class DealershipUserBase(BaseModel):
    role: UserRole
    is_active: bool = True


class DealershipUserCreate(DealershipUserBase):
    user_id: int
    dealership_id: int


class DealershipUserUpdate(BaseModel):
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None


class DealershipUserInDB(DealershipUserBase):
    id: int
    dealership_id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class DealershipUser(DealershipUserInDB):
    """Dealership user association for API responses"""
    pass


class UserDealership(BaseModel):
    """Simplified dealership info for user's dealership list"""
    id: int
    name: str
    role: UserRole
    is_active: bool
    lightspeed_connected: bool = False
    
    class Config:
        from_attributes = True
