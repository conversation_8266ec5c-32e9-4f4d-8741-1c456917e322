from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, validator
from enum import Enum


class LightspeedAuthResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int
    domain_prefix: str
    scope: str


class LightspeedTokenRefresh(BaseModel):
    refresh_token: str


class LightspeedConnectionStatus(BaseModel):
    is_connected: bool
    shop_name: Optional[str] = None
    last_sync: Optional[datetime] = None
    sync_status: Optional[str] = None
    error_message: Optional[str] = None


class LightspeedProduct(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    sku: Optional[str] = None
    brand: Optional[str] = None
    retail_price: Optional[float] = None
    supply_price: Optional[float] = None
    inventory: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class LightspeedSale(BaseModel):
    id: str
    sale_date: datetime
    total_price: float
    total_tax: float
    customer_id: Optional[str] = None
    customer_name: Optional[str] = None
    status: str
    line_items: List[Dict[str, Any]] = []
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class LightspeedCustomer(BaseModel):
    id: str
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class LightspeedInventory(BaseModel):
    product_id: str
    outlet_id: str
    count: float
    reorder_point: Optional[float] = None
    restock_level: Optional[float] = None
    updated_at: Optional[datetime] = None


class SyncRequest(BaseModel):
    sync_type: str = "manual"
    sync_inventory: bool = True
    sync_sales: bool = True
    sync_customers: bool = True
    sync_categories: bool = True


class SyncResponse(BaseModel):
    task_id: str
    status: str
    message: str


class LightspeedWebhook(BaseModel):
    id: str
    url: str
    active: bool
    events: List[str]
    created_at: datetime
    updated_at: datetime


class WebhookEvent(BaseModel):
    id: str
    type: str
    data: Dict[str, Any]
    created_at: datetime
    
    @validator('type')
    def validate_event_type(cls, v):
        valid_types = [
            'product.created', 'product.updated', 'product.deleted',
            'sale.created', 'sale.updated', 'sale.deleted',
            'customer.created', 'customer.updated', 'customer.deleted',
            'inventory.updated'
        ]
        if v not in valid_types:
            raise ValueError(f'Invalid event type: {v}')
        return v
