from sqlalchemy import Column, Inte<PERSON>, <PERSON>, Boolean, DateTime, Text, ForeignKey, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.core.database import Base


class SyncStatus(PyEnum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SyncType(PyEnum):
    FULL = "full"
    INCREMENTAL = "incremental"
    MANUAL = "manual"


class LightspeedIntegration(Base):
    __tablename__ = "lightspeed_integrations"
    
    id = Column(Integer, primary_key=True, index=True)
    dealership_id = Column(Integer, ForeignKey("dealerships.id"), nullable=False, unique=True)
    
    # OAuth credentials
    access_token = Column(Text, nullable=True)  # Encrypted in production
    refresh_token = Column(Text, nullable=True)  # Encrypted in production
    token_expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Lightspeed account info
    account_id = Column(String(50), nullable=True)
    shop_id = Column(String(50), nullable=True)
    shop_name = Column(String(255), nullable=True)
    
    # Integration settings
    is_active = Column(Boolean, default=True)
    auto_sync_enabled = Column(Boolean, default=True)
    sync_frequency_minutes = Column(Integer, default=60)  # How often to sync
    
    # Sync configuration
    sync_inventory = Column(Boolean, default=True)
    sync_sales = Column(Boolean, default=True)
    sync_customers = Column(Boolean, default=True)
    sync_categories = Column(Boolean, default=True)
    
    # Last sync information
    last_sync_at = Column(DateTime(timezone=True), nullable=True)
    last_successful_sync_at = Column(DateTime(timezone=True), nullable=True)
    last_sync_status = Column(Enum(SyncStatus), nullable=True)
    last_sync_error = Column(Text, nullable=True)
    
    # Sync statistics
    total_syncs = Column(Integer, default=0)
    successful_syncs = Column(Integer, default=0)
    failed_syncs = Column(Integer, default=0)
    
    # Rate limiting
    api_calls_today = Column(Integer, default=0)
    api_limit_reset_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    dealership = relationship("Dealership", back_populates="lightspeed_integration")
    sync_logs = relationship("SyncLog", back_populates="integration", cascade="all, delete-orphan")
    
    @property
    def is_token_expired(self) -> bool:
        """Check if access token is expired"""
        if not self.token_expires_at:
            return True
        return self.token_expires_at <= func.now()
    
    @property
    def sync_success_rate(self) -> float:
        """Calculate sync success rate percentage"""
        if self.total_syncs == 0:
            return 0.0
        return (self.successful_syncs / self.total_syncs) * 100
    
    def __repr__(self):
        return f"<LightspeedIntegration(id={self.id}, dealership_id={self.dealership_id}, shop_name='{self.shop_name}')>"


class SyncLog(Base):
    __tablename__ = "sync_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    integration_id = Column(Integer, ForeignKey("lightspeed_integrations.id"), nullable=False)
    
    # Sync details
    sync_type = Column(Enum(SyncType), nullable=False)
    status = Column(Enum(SyncStatus), nullable=False)
    
    # What was synced
    synced_inventory = Column(Boolean, default=False)
    synced_sales = Column(Boolean, default=False)
    synced_customers = Column(Boolean, default=False)
    synced_categories = Column(Boolean, default=False)
    
    # Sync statistics
    records_processed = Column(Integer, default=0)
    records_created = Column(Integer, default=0)
    records_updated = Column(Integer, default=0)
    records_failed = Column(Integer, default=0)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # Performance metrics
    duration_seconds = Column(Integer, nullable=True)
    api_calls_made = Column(Integer, default=0)
    
    # Timestamps
    started_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    integration = relationship("LightspeedIntegration", back_populates="sync_logs")
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate for this sync"""
        if self.records_processed == 0:
            return 0.0
        successful = self.records_created + self.records_updated
        return (successful / self.records_processed) * 100
    
    def __repr__(self):
        return f"<SyncLog(id={self.id}, sync_type={self.sync_type}, status={self.status})>"
