from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Numeric, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.core.database import Base


class SaleStatus(PyEnum):
    PENDING = "pending"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"


class PaymentMethod(PyEnum):
    CASH = "cash"
    CREDIT_CARD = "credit_card"
    DEBIT_CARD = "debit_card"
    CHECK = "check"
    FINANCING = "financing"
    TRADE_IN = "trade_in"
    OTHER = "other"


class Sale(Base):
    __tablename__ = "sales"
    
    id = Column(Integer, primary_key=True, index=True)
    dealership_id = Column(Integer, ForeignKey("dealerships.id"), nullable=False)
    
    # Sale identification
    sale_number = Column(String(100), nullable=False, index=True)
    invoice_number = Column(String(100), nullable=True)
    
    # Customer information
    customer_name = Column(String(255), nullable=True)
    customer_email = Column(String(255), nullable=True)
    customer_phone = Column(String(20), nullable=True)
    customer_address = Column(Text, nullable=True)
    
    # Sale details
    subtotal = Column(Numeric(10, 2), nullable=False, default=0)
    tax_amount = Column(Numeric(10, 2), nullable=False, default=0)
    discount_amount = Column(Numeric(10, 2), nullable=False, default=0)
    total_amount = Column(Numeric(10, 2), nullable=False, default=0)
    
    # Payment
    payment_method = Column(Enum(PaymentMethod), nullable=True)
    payment_status = Column(String(50), default="pending")
    
    # Status and tracking
    status = Column(Enum(SaleStatus), default=SaleStatus.PENDING)
    notes = Column(Text, nullable=True)
    
    # Sales person
    salesperson_name = Column(String(255), nullable=True)
    salesperson_id = Column(Integer, nullable=True)  # Could link to User table
    
    # Lightspeed integration
    lightspeed_id = Column(String(50), nullable=True, index=True)
    lightspeed_data = Column(JSON, nullable=True)
    
    # AI insights
    ai_insights = Column(JSON, nullable=True)
    customer_sentiment = Column(String(50), nullable=True)  # positive, neutral, negative
    
    # Timestamps
    sale_date = Column(DateTime(timezone=True), nullable=False, default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    dealership = relationship("Dealership", back_populates="sales")
    sale_items = relationship("SaleItem", back_populates="sale", cascade="all, delete-orphan")
    
    @property
    def item_count(self) -> int:
        """Total number of items in the sale"""
        return sum(item.quantity for item in self.sale_items)
    
    def __repr__(self):
        return f"<Sale(id={self.id}, sale_number='{self.sale_number}', total={self.total_amount})>"


class SaleItem(Base):
    __tablename__ = "sale_items"
    
    id = Column(Integer, primary_key=True, index=True)
    sale_id = Column(Integer, ForeignKey("sales.id"), nullable=False)
    inventory_item_id = Column(Integer, ForeignKey("inventory_items.id"), nullable=True)
    
    # Item details (stored separately in case inventory item is deleted)
    sku = Column(String(100), nullable=False)
    name = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    
    # Pricing and quantity
    quantity = Column(Integer, nullable=False, default=1)
    unit_price = Column(Numeric(10, 2), nullable=False)
    discount_amount = Column(Numeric(10, 2), default=0)
    total_price = Column(Numeric(10, 2), nullable=False)
    
    # Cost tracking for profit calculation
    unit_cost = Column(Numeric(10, 2), nullable=True)
    
    # Serial numbers for serialized items
    serial_numbers = Column(JSON, nullable=True)  # Array of serial numbers
    
    # Lightspeed integration
    lightspeed_id = Column(String(50), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    sale = relationship("Sale", back_populates="sale_items")
    inventory_item = relationship("InventoryItem", back_populates="sale_items")
    
    @property
    def profit(self) -> float:
        """Calculate profit for this line item"""
        if self.unit_cost:
            return (self.unit_price - self.unit_cost) * self.quantity
        return 0.0
    
    @property
    def margin_percentage(self) -> float:
        """Calculate margin percentage"""
        if self.unit_cost and self.unit_cost > 0:
            return ((self.unit_price - self.unit_cost) / self.unit_cost) * 100
        return 0.0
    
    def __repr__(self):
        return f"<SaleItem(id={self.id}, sku='{self.sku}', quantity={self.quantity})>"
