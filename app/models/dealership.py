from sqlalchemy import Column, Integer, String, Bo<PERSON>an, DateTime, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.core.database import Base


class UserRole(PyEnum):
    OWNER = "owner"
    MANAGER = "manager"
    SALES = "sales"
    VIEWER = "viewer"


class Dealership(Base):
    __tablename__ = "dealerships"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    business_name = Column(String(255), nullable=True)
    address = Column(Text, nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(50), nullable=True)
    zip_code = Column(String(20), nullable=True)
    country = Column(String(50), default="US")
    phone = Column(String(20), nullable=True)
    email = Column(String(255), nullable=True)
    website = Column(String(500), nullable=True)
    
    # Business details
    tax_id = Column(String(50), nullable=True)
    license_number = Column(String(100), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True)
    subscription_tier = Column(String(50), default="basic")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user_associations = relationship("DealershipUser", back_populates="dealership")
    lightspeed_integration = relationship("LightspeedIntegration", back_populates="dealership", uselist=False)
    inventory_items = relationship("InventoryItem", back_populates="dealership")
    sales = relationship("Sale", back_populates="dealership")
    
    def __repr__(self):
        return f"<Dealership(id={self.id}, name='{self.name}')>"


class DealershipUser(Base):
    __tablename__ = "dealership_users"
    
    id = Column(Integer, primary_key=True, index=True)
    dealership_id = Column(Integer, ForeignKey("dealerships.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    role = Column(Enum(UserRole), nullable=False, default=UserRole.VIEWER)
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    dealership = relationship("Dealership", back_populates="user_associations")
    user = relationship("User", back_populates="dealership_associations")
    
    def __repr__(self):
        return f"<DealershipUser(dealership_id={self.dealership_id}, user_id={self.user_id}, role={self.role})>"
