from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Numeric, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class Category(Base):
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    parent_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    
    # Lightspeed integration
    lightspeed_id = Column(String(50), nullable=True, index=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    parent = relationship("Category", remote_side=[id])
    inventory_items = relationship("InventoryItem", back_populates="category")
    
    def __repr__(self):
        return f"<Category(id={self.id}, name='{self.name}')>"


class InventoryItem(Base):
    __tablename__ = "inventory_items"
    
    id = Column(Integer, primary_key=True, index=True)
    dealership_id = Column(Integer, ForeignKey("dealerships.id"), nullable=False)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    
    # Basic item information
    sku = Column(String(100), nullable=False, index=True)
    name = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    brand = Column(String(100), nullable=True)
    model = Column(String(100), nullable=True)
    year = Column(Integer, nullable=True)
    
    # Pricing
    cost_price = Column(Numeric(10, 2), nullable=True)
    retail_price = Column(Numeric(10, 2), nullable=True)
    sale_price = Column(Numeric(10, 2), nullable=True)
    
    # Inventory tracking
    quantity_on_hand = Column(Integer, default=0)
    quantity_available = Column(Integer, default=0)
    quantity_committed = Column(Integer, default=0)
    reorder_level = Column(Integer, default=0)
    reorder_quantity = Column(Integer, default=0)
    
    # Physical attributes
    weight = Column(Numeric(8, 2), nullable=True)
    dimensions = Column(String(100), nullable=True)  # e.g., "10x5x3 inches"
    color = Column(String(50), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True)
    is_serialized = Column(Boolean, default=False)
    
    # Lightspeed integration
    lightspeed_id = Column(String(50), nullable=True, index=True)
    lightspeed_data = Column(JSON, nullable=True)  # Store raw Lightspeed data
    
    # AI-generated insights
    ai_insights = Column(JSON, nullable=True)  # Store AI-generated insights
    demand_forecast = Column(JSON, nullable=True)  # Store demand forecasting data
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_sold = Column(DateTime(timezone=True), nullable=True)
    last_received = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    dealership = relationship("Dealership", back_populates="inventory_items")
    category = relationship("Category", back_populates="inventory_items")
    sale_items = relationship("SaleItem", back_populates="inventory_item")
    
    @property
    def margin(self) -> float:
        """Calculate profit margin percentage"""
        if self.cost_price and self.retail_price and self.cost_price > 0:
            return ((self.retail_price - self.cost_price) / self.cost_price) * 100
        return 0.0
    
    @property
    def is_low_stock(self) -> bool:
        """Check if item is below reorder level"""
        return self.quantity_available <= self.reorder_level
    
    def __repr__(self):
        return f"<InventoryItem(id={self.id}, sku='{self.sku}', name='{self.name}')>"
