import asyncio
from datetime import datetime
from celery import current_task
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.celery import celery_app
from app.core.database import AsyncSessionLocal
from app.services.data_pipeline_service import DataPipelineService
from app.services.ai_service import AIService
from app.models.dealership import Dealership
from app.models.lightspeed import LightspeedIntegration
import structlog

logger = structlog.get_logger()


async def run_data_pipeline_for_dealership(dealership_id: int):
    """Run the complete data pipeline for a dealership"""
    async with AsyncSessionLocal() as db:
        try:
            pipeline_service = DataPipelineService(db)

            # Run the complete pipeline
            metrics = await pipeline_service.process_dealership_data(dealership_id)

            logger.info("Data pipeline completed",
                       dealership_id=dealership_id,
                       metrics=metrics.__dict__)

            return {
                "status": "success",
                "dealership_id": dealership_id,
                "metrics": {
                    "records_processed": metrics.records_processed,
                    "records_enriched": metrics.records_enriched,
                    "insights_generated": metrics.insights_generated,
                    "errors": metrics.errors,
                    "execution_time": metrics.execution_time
                }
            }

        except Exception as e:
            logger.error("Data pipeline failed",
                        dealership_id=dealership_id,
                        error=str(e))
            raise e


@celery_app.task(bind=True)
def process_dealership_data(self, dealership_id: int):
    """Process and enrich data for a specific dealership"""
    try:
        logger.info("Starting data processing pipeline", dealership_id=dealership_id)

        result = asyncio.run(run_data_pipeline_for_dealership(dealership_id))
        return result

    except Exception as exc:
        logger.error("Data processing pipeline failed",
                    dealership_id=dealership_id,
                    error=str(exc))
        raise self.retry(exc=exc, countdown=300, max_retries=3)  # 5 minute retry


@celery_app.task(bind=True)
def generate_inventory_insights(self, dealership_id: int):
    """Generate AI insights for inventory items"""
    async def run_inventory_insights():
        async with AsyncSessionLocal() as db:
            try:
                pipeline_service = DataPipelineService(db)

                # Extract dealership data
                data = await pipeline_service._extract_dealership_data(dealership_id)

                # Process only inventory data
                metrics = await pipeline_service._process_inventory_data(dealership_id, data)

                logger.info("Inventory insights generated",
                           dealership_id=dealership_id,
                           insights_count=metrics.insights_generated)

                return {
                    "status": "success",
                    "dealership_id": dealership_id,
                    "insights_generated": metrics.insights_generated
                }

            except Exception as e:
                logger.error("Failed to generate inventory insights",
                           dealership_id=dealership_id,
                           error=str(e))
                raise e

    try:
        result = asyncio.run(run_inventory_insights())
        return result

    except Exception as exc:
        logger.error("Inventory insights task failed",
                    dealership_id=dealership_id,
                    error=str(exc))
        raise self.retry(exc=exc, countdown=60, max_retries=3)


@celery_app.task(bind=True)
def generate_sales_insights(self, dealership_id: int):
    """Generate AI insights for sales data"""
    async def run_sales_insights():
        async with AsyncSessionLocal() as db:
            try:
                pipeline_service = DataPipelineService(db)

                # Extract dealership data
                data = await pipeline_service._extract_dealership_data(dealership_id)

                # Process only sales data
                metrics = await pipeline_service._process_sales_data(dealership_id, data)

                logger.info("Sales insights generated",
                           dealership_id=dealership_id,
                           insights_count=metrics.insights_generated)

                return {
                    "status": "success",
                    "dealership_id": dealership_id,
                    "insights_generated": metrics.insights_generated
                }

            except Exception as e:
                logger.error("Failed to generate sales insights",
                           dealership_id=dealership_id,
                           error=str(e))
                raise e

    try:
        result = asyncio.run(run_sales_insights())
        return result

    except Exception as exc:
        logger.error("Sales insights task failed",
                    dealership_id=dealership_id,
                    error=str(exc))
        raise self.retry(exc=exc, countdown=60, max_retries=3)


@celery_app.task
def generate_daily_insights():
    """Generate daily AI insights for all active dealerships"""
    async def run_daily_insights():
        async with AsyncSessionLocal() as db:
            try:
                # Get all active dealerships with Lightspeed integration
                result = await db.execute(
                    select(Dealership.id)
                    .join(LightspeedIntegration)
                    .where(
                        Dealership.is_active == True,
                        LightspeedIntegration.is_active == True
                    )
                )
                dealership_ids = [row[0] for row in result.fetchall()]

                logger.info("Starting daily insights generation",
                           dealership_count=len(dealership_ids))

                # Queue processing tasks for each dealership
                for dealership_id in dealership_ids:
                    process_dealership_data.delay(dealership_id)

                logger.info("Daily insights tasks queued",
                           dealership_count=len(dealership_ids))

                return {
                    "status": "success",
                    "dealerships_queued": len(dealership_ids)
                }

            except Exception as e:
                logger.error("Failed to queue daily insights", error=str(e))
                raise e

    try:
        result = asyncio.run(run_daily_insights())
        return result

    except Exception as exc:
        logger.error("Daily insights task failed", error=str(exc))
        raise exc


@celery_app.task(bind=True)
def generate_demand_forecast(self, dealership_id: int, sku: str):
    """Generate demand forecast for a specific item"""
    async def run_forecast():
        async with AsyncSessionLocal() as db:
            try:
                ai_service = AIService()

                # Get historical sales data for the item
                # This would involve querying sales history
                historical_data = []  # Placeholder
                item_info = {"sku": sku, "dealership_id": dealership_id}

                # Generate forecast
                forecast = await ai_service.generate_demand_forecast(historical_data, item_info)

                logger.info("Demand forecast generated",
                           dealership_id=dealership_id,
                           sku=sku)

                return {
                    "status": "success",
                    "dealership_id": dealership_id,
                    "sku": sku,
                    "forecast": forecast
                }

            except Exception as e:
                logger.error("Failed to generate demand forecast",
                           dealership_id=dealership_id,
                           sku=sku,
                           error=str(e))
                raise e

    try:
        result = asyncio.run(run_forecast())
        return result

    except Exception as exc:
        logger.error("Demand forecast task failed",
                    dealership_id=dealership_id,
                    sku=sku,
                    error=str(exc))
        raise self.retry(exc=exc, countdown=120, max_retries=2)
