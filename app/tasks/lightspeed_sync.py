import asyncio
from datetime import datetime
from celery import current_task
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.celery import celery_app
from app.core.database import AsyncSessionLocal
from app.services.lightspeed_service import LightspeedService, LightspeedAPIError
from app.services.inventory_sync_service import InventorySyncService
from app.services.sales_sync_service import SalesSyncService
from app.models.lightspeed import LightspeedIntegration, SyncLog, SyncStatus, SyncType
from app.models.dealership import Dealership
import structlog

logger = structlog.get_logger()


async def run_sync_for_dealership(dealership_id: int, sync_type: SyncType = SyncType.MANUAL):
    """Run sync for a specific dealership"""
    async with AsyncSessionLocal() as db:
        try:
            # Get integration
            lightspeed_service = LightspeedService(db)
            integration = await lightspeed_service.get_integration_by_dealership(dealership_id)

            if not integration or not integration.is_active:
                logger.error("No active integration found", dealership_id=dealership_id)
                return {"status": "error", "message": "No active integration found"}

            # Create sync log
            sync_log = SyncLog(
                integration_id=integration.id,
                sync_type=sync_type,
                status=SyncStatus.IN_PROGRESS,
                started_at=datetime.utcnow()
            )
            db.add(sync_log)
            await db.commit()
            await db.refresh(sync_log)

            # Update integration status
            integration.last_sync_at = datetime.utcnow()
            integration.last_sync_status = SyncStatus.IN_PROGRESS

            records_processed = 0
            records_created = 0
            records_updated = 0
            records_failed = 0

            # Sync inventory if enabled
            if integration.sync_inventory:
                try:
                    inventory_service = InventorySyncService(db, lightspeed_service)
                    inventory_stats = await inventory_service.sync_inventory(integration)

                    records_processed += inventory_stats.get("processed", 0)
                    records_created += inventory_stats.get("created", 0)
                    records_updated += inventory_stats.get("updated", 0)
                    records_failed += inventory_stats.get("failed", 0)

                    sync_log.synced_inventory = True
                    logger.info("Inventory sync completed", dealership_id=dealership_id, stats=inventory_stats)

                except Exception as e:
                    logger.error("Inventory sync failed", dealership_id=dealership_id, error=str(e))
                    records_failed += 1

            # Sync sales if enabled
            if integration.sync_sales:
                try:
                    sales_service = SalesSyncService(db, lightspeed_service)
                    sales_stats = await sales_service.sync_sales(integration)

                    records_processed += sales_stats.get("processed", 0)
                    records_created += sales_stats.get("created", 0)
                    records_updated += sales_stats.get("updated", 0)
                    records_failed += sales_stats.get("failed", 0)

                    sync_log.synced_sales = True
                    logger.info("Sales sync completed", dealership_id=dealership_id, stats=sales_stats)

                except Exception as e:
                    logger.error("Sales sync failed", dealership_id=dealership_id, error=str(e))
                    records_failed += 1

            # Update sync log with results
            sync_log.records_processed = records_processed
            sync_log.records_created = records_created
            sync_log.records_updated = records_updated
            sync_log.records_failed = records_failed
            sync_log.status = SyncStatus.COMPLETED if records_failed == 0 else SyncStatus.FAILED
            sync_log.completed_at = datetime.utcnow()

            # Update integration
            integration.last_sync_status = sync_log.status
            integration.total_syncs += 1

            if sync_log.status == SyncStatus.COMPLETED:
                integration.successful_syncs += 1
                integration.last_successful_sync_at = datetime.utcnow()
            else:
                integration.failed_syncs += 1

            await db.commit()

            logger.info("Sync completed",
                       dealership_id=dealership_id,
                       status=sync_log.status.value,
                       processed=records_processed,
                       created=records_created,
                       updated=records_updated,
                       failed=records_failed)

            return {
                "status": "success" if sync_log.status == SyncStatus.COMPLETED else "partial",
                "dealership_id": dealership_id,
                "records_processed": records_processed,
                "records_created": records_created,
                "records_updated": records_updated,
                "records_failed": records_failed
            }

        except Exception as exc:
            logger.error("Sync failed", dealership_id=dealership_id, error=str(exc))

            # Update sync log with error
            if 'sync_log' in locals():
                sync_log.status = SyncStatus.FAILED
                sync_log.error_message = str(exc)
                sync_log.completed_at = datetime.utcnow()
                await db.commit()

            # Update integration
            if 'integration' in locals():
                integration.last_sync_status = SyncStatus.FAILED
                integration.last_sync_error = str(exc)
                integration.failed_syncs += 1
                await db.commit()

            raise exc


@celery_app.task(bind=True)
def sync_dealership_data(self, dealership_id: int, sync_type: str = "manual"):
    """Sync data for a specific dealership"""
    try:
        sync_type_enum = SyncType(sync_type)
        result = asyncio.run(run_sync_for_dealership(dealership_id, sync_type_enum))
        return result

    except Exception as exc:
        logger.error("Lightspeed sync task failed", dealership_id=dealership_id, error=str(exc))
        raise self.retry(exc=exc, countdown=60, max_retries=3)


@celery_app.task
def sync_all_dealerships():
    """Sync data for all active dealerships"""
    async def run_all_syncs():
        async with AsyncSessionLocal() as db:
            try:
                # Get all active integrations
                result = await db.execute(
                    select(LightspeedIntegration)
                    .join(Dealership)
                    .where(
                        LightspeedIntegration.is_active == True,
                        LightspeedIntegration.auto_sync_enabled == True,
                        Dealership.is_active == True
                    )
                )
                integrations = result.scalars().all()

                logger.info("Starting sync for all dealerships", count=len(integrations))

                # Trigger sync tasks for each dealership
                for integration in integrations:
                    sync_dealership_data.delay(integration.dealership_id, "incremental")

                logger.info("All dealership syncs initiated", count=len(integrations))
                return {"status": "success", "dealerships_queued": len(integrations)}

            except Exception as exc:
                logger.error("Failed to initiate all dealership syncs", error=str(exc))
                raise exc

    try:
        result = asyncio.run(run_all_syncs())
        return result
    except Exception as exc:
        logger.error("Sync all dealerships task failed", error=str(exc))
        raise exc
