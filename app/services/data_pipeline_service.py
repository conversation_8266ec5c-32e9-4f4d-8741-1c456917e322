from typing import Dict, Any, List, Optional
from datetime import datetime, timed<PERSON>ta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
import pandas as pd
import numpy as np
from dataclasses import dataclass
import structlog

from app.models.dealership import Dealership
from app.models.inventory import InventoryItem
from app.models.sales import Sale, SaleItem
from app.models.lightspeed import LightspeedIntegration
from app.services.ai_service import AIService
from app.core.redis import redis_client

logger = structlog.get_logger()


@dataclass
class PipelineMetrics:
    """Metrics for pipeline execution"""
    records_processed: int = 0
    records_enriched: int = 0
    insights_generated: int = 0
    errors: int = 0
    execution_time: float = 0.0


class DataPipelineService:
    """Service for processing and enriching dealership data"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.ai_service = AIService()
    
    async def process_dealership_data(self, dealership_id: int) -> PipelineMetrics:
        """Main pipeline to process all data for a dealership"""
        start_time = datetime.utcnow()
        metrics = PipelineMetrics()
        
        try:
            logger.info("Starting data pipeline", dealership_id=dealership_id)
            
            # 1. Extract and prepare data
            dealership_data = await self._extract_dealership_data(dealership_id)
            
            # 2. Process inventory data
            inventory_metrics = await self._process_inventory_data(dealership_id, dealership_data)
            metrics.records_processed += inventory_metrics.records_processed
            metrics.insights_generated += inventory_metrics.insights_generated
            
            # 3. Process sales data
            sales_metrics = await self._process_sales_data(dealership_id, dealership_data)
            metrics.records_processed += sales_metrics.records_processed
            metrics.insights_generated += sales_metrics.insights_generated
            
            # 4. Generate cross-functional insights
            cross_insights = await self._generate_cross_functional_insights(dealership_id, dealership_data)
            metrics.insights_generated += len(cross_insights)
            
            # 5. Update cache with processed data
            await self._update_cache(dealership_id, dealership_data, cross_insights)
            
            metrics.execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.info("Data pipeline completed", 
                       dealership_id=dealership_id,
                       metrics=metrics.__dict__)
            
            return metrics
            
        except Exception as e:
            metrics.errors += 1
            metrics.execution_time = (datetime.utcnow() - start_time).total_seconds()
            logger.error("Data pipeline failed", 
                        dealership_id=dealership_id,
                        error=str(e))
            raise e
    
    async def _extract_dealership_data(self, dealership_id: int) -> Dict[str, Any]:
        """Extract all relevant data for a dealership"""
        
        # Get dealership info
        dealership_result = await self.db.execute(
            select(Dealership).where(Dealership.id == dealership_id)
        )
        dealership = dealership_result.scalar_one_or_none()
        
        if not dealership:
            raise ValueError(f"Dealership {dealership_id} not found")
        
        # Get inventory data
        inventory_result = await self.db.execute(
            select(InventoryItem)
            .where(
                InventoryItem.dealership_id == dealership_id,
                InventoryItem.is_active == True
            )
        )
        inventory_items = inventory_result.scalars().all()
        
        # Get sales data (last 90 days)
        ninety_days_ago = datetime.utcnow() - timedelta(days=90)
        sales_result = await self.db.execute(
            select(Sale)
            .where(
                Sale.dealership_id == dealership_id,
                Sale.sale_date >= ninety_days_ago
            )
            .order_by(Sale.sale_date.desc())
        )
        sales = sales_result.scalars().all()
        
        # Get sale items for the sales
        sale_ids = [sale.id for sale in sales]
        if sale_ids:
            sale_items_result = await self.db.execute(
                select(SaleItem).where(SaleItem.sale_id.in_(sale_ids))
            )
            sale_items = sale_items_result.scalars().all()
        else:
            sale_items = []
        
        return {
            "dealership": dealership,
            "inventory_items": inventory_items,
            "sales": sales,
            "sale_items": sale_items,
            "extracted_at": datetime.utcnow()
        }
    
    async def _process_inventory_data(self, dealership_id: int, data: Dict[str, Any]) -> PipelineMetrics:
        """Process and enrich inventory data"""
        metrics = PipelineMetrics()
        inventory_items = data["inventory_items"]
        
        logger.info("Processing inventory data", 
                   dealership_id=dealership_id,
                   item_count=len(inventory_items))
        
        # Convert to DataFrame for analysis
        inventory_df = self._inventory_to_dataframe(inventory_items)
        
        if inventory_df.empty:
            return metrics
        
        # Calculate inventory metrics
        inventory_insights = await self._calculate_inventory_insights(inventory_df)
        
        # Generate AI-powered insights for each item
        for item in inventory_items:
            try:
                # Prepare item data for AI analysis
                item_data = {
                    "sku": item.sku,
                    "name": item.name,
                    "brand": item.brand,
                    "category": item.category.name if item.category else None,
                    "quantity_on_hand": item.quantity_on_hand,
                    "quantity_available": item.quantity_available,
                    "retail_price": float(item.retail_price) if item.retail_price else None,
                    "cost_price": float(item.cost_price) if item.cost_price else None,
                    "reorder_level": item.reorder_level,
                    "last_sold": item.last_sold.isoformat() if item.last_sold else None,
                    "margin": item.margin
                }
                
                # Generate AI insights
                ai_insights = await self.ai_service.generate_inventory_insights(
                    item_data, 
                    inventory_insights
                )
                
                # Update item with insights
                item.ai_insights = ai_insights
                metrics.records_processed += 1
                metrics.insights_generated += 1
                
            except Exception as e:
                logger.error("Failed to process inventory item", 
                           item_id=item.id,
                           error=str(e))
                metrics.errors += 1
        
        await self.db.commit()
        return metrics
    
    async def _process_sales_data(self, dealership_id: int, data: Dict[str, Any]) -> PipelineMetrics:
        """Process and enrich sales data"""
        metrics = PipelineMetrics()
        sales = data["sales"]
        sale_items = data["sale_items"]
        
        logger.info("Processing sales data", 
                   dealership_id=dealership_id,
                   sales_count=len(sales))
        
        # Convert to DataFrame for analysis
        sales_df = self._sales_to_dataframe(sales, sale_items)
        
        if sales_df.empty:
            return metrics
        
        # Calculate sales metrics
        sales_insights = await self._calculate_sales_insights(sales_df)
        
        # Generate AI insights for sales patterns
        for sale in sales:
            try:
                # Prepare sale data for AI analysis
                sale_data = {
                    "sale_id": sale.id,
                    "sale_date": sale.sale_date.isoformat(),
                    "total_amount": float(sale.total_amount),
                    "item_count": sale.item_count,
                    "customer_name": sale.customer_name,
                    "payment_method": sale.payment_method.value if sale.payment_method else None,
                    "status": sale.status.value
                }
                
                # Get sale items for this sale
                sale_sale_items = [item for item in sale_items if item.sale_id == sale.id]
                sale_data["items"] = [
                    {
                        "sku": item.sku,
                        "name": item.name,
                        "quantity": item.quantity,
                        "unit_price": float(item.unit_price),
                        "total_price": float(item.total_price),
                        "profit": item.profit
                    }
                    for item in sale_sale_items
                ]
                
                # Generate AI insights
                ai_insights = await self.ai_service.generate_sales_insights(
                    sale_data,
                    sales_insights
                )
                
                # Update sale with insights
                sale.ai_insights = ai_insights
                metrics.records_processed += 1
                metrics.insights_generated += 1
                
            except Exception as e:
                logger.error("Failed to process sale", 
                           sale_id=sale.id,
                           error=str(e))
                metrics.errors += 1
        
        await self.db.commit()
        return metrics
    
    def _inventory_to_dataframe(self, inventory_items: List[InventoryItem]) -> pd.DataFrame:
        """Convert inventory items to pandas DataFrame"""
        if not inventory_items:
            return pd.DataFrame()
        
        data = []
        for item in inventory_items:
            data.append({
                "id": item.id,
                "sku": item.sku,
                "name": item.name,
                "brand": item.brand,
                "category": item.category.name if item.category else "Unknown",
                "quantity_on_hand": item.quantity_on_hand,
                "quantity_available": item.quantity_available,
                "retail_price": float(item.retail_price) if item.retail_price else 0,
                "cost_price": float(item.cost_price) if item.cost_price else 0,
                "margin": item.margin,
                "reorder_level": item.reorder_level,
                "is_low_stock": item.is_low_stock,
                "last_sold": item.last_sold,
                "created_at": item.created_at,
                "updated_at": item.updated_at
            })
        
        return pd.DataFrame(data)
    
    def _sales_to_dataframe(self, sales: List[Sale], sale_items: List[SaleItem]) -> pd.DataFrame:
        """Convert sales data to pandas DataFrame"""
        if not sales:
            return pd.DataFrame()
        
        data = []
        for sale in sales:
            # Get items for this sale
            items = [item for item in sale_items if item.sale_id == sale.id]
            
            data.append({
                "id": sale.id,
                "sale_date": sale.sale_date,
                "total_amount": float(sale.total_amount),
                "tax_amount": float(sale.tax_amount),
                "item_count": len(items),
                "customer_name": sale.customer_name,
                "payment_method": sale.payment_method.value if sale.payment_method else None,
                "status": sale.status.value,
                "profit": sum(item.profit for item in items),
                "created_at": sale.created_at
            })
        
        return pd.DataFrame(data)
    
    async def _calculate_inventory_insights(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate inventory-level insights"""
        insights = {
            "total_items": len(df),
            "total_value": df["retail_price"].sum(),
            "avg_margin": df["margin"].mean(),
            "low_stock_count": df["is_low_stock"].sum(),
            "top_brands": df["brand"].value_counts().head(10).to_dict(),
            "category_distribution": df["category"].value_counts().to_dict(),
            "price_ranges": {
                "under_100": len(df[df["retail_price"] < 100]),
                "100_500": len(df[(df["retail_price"] >= 100) & (df["retail_price"] < 500)]),
                "500_1000": len(df[(df["retail_price"] >= 500) & (df["retail_price"] < 1000)]),
                "over_1000": len(df[df["retail_price"] >= 1000])
            }
        }
        
        return insights
    
    async def _calculate_sales_insights(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate sales-level insights"""
        if df.empty:
            return {}
        
        # Time-based analysis
        df["sale_date"] = pd.to_datetime(df["sale_date"])
        df["day_of_week"] = df["sale_date"].dt.day_name()
        df["hour"] = df["sale_date"].dt.hour
        df["month"] = df["sale_date"].dt.month
        
        insights = {
            "total_sales": len(df),
            "total_revenue": df["total_amount"].sum(),
            "avg_sale_amount": df["total_amount"].mean(),
            "total_profit": df["profit"].sum(),
            "avg_profit_margin": (df["profit"] / df["total_amount"]).mean() * 100,
            "sales_by_day": df["day_of_week"].value_counts().to_dict(),
            "sales_by_hour": df["hour"].value_counts().to_dict(),
            "payment_methods": df["payment_method"].value_counts().to_dict(),
            "monthly_trend": df.groupby("month")["total_amount"].sum().to_dict()
        }
        
        return insights

    async def _generate_cross_functional_insights(self, dealership_id: int, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate insights that span multiple data sources"""
        insights = []

        try:
            # Inventory turnover analysis
            inventory_turnover = await self._calculate_inventory_turnover(data)
            if inventory_turnover:
                insights.append({
                    "type": "inventory_turnover",
                    "data": inventory_turnover,
                    "generated_at": datetime.utcnow()
                })

            # Seasonal trends
            seasonal_trends = await self._analyze_seasonal_trends(data)
            if seasonal_trends:
                insights.append({
                    "type": "seasonal_trends",
                    "data": seasonal_trends,
                    "generated_at": datetime.utcnow()
                })

            # Customer behavior patterns
            customer_patterns = await self._analyze_customer_patterns(data)
            if customer_patterns:
                insights.append({
                    "type": "customer_patterns",
                    "data": customer_patterns,
                    "generated_at": datetime.utcnow()
                })

            # Profitability analysis
            profitability = await self._analyze_profitability(data)
            if profitability:
                insights.append({
                    "type": "profitability_analysis",
                    "data": profitability,
                    "generated_at": datetime.utcnow()
                })

        except Exception as e:
            logger.error("Failed to generate cross-functional insights",
                        dealership_id=dealership_id,
                        error=str(e))

        return insights

    async def _calculate_inventory_turnover(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Calculate inventory turnover rates"""
        inventory_items = data["inventory_items"]
        sale_items = data["sale_items"]

        if not inventory_items or not sale_items:
            return None

        # Create mapping of SKU to inventory
        inventory_map = {item.sku: item for item in inventory_items}

        # Calculate turnover for each item
        turnover_data = {}
        for sale_item in sale_items:
            sku = sale_item.sku
            if sku in inventory_map:
                if sku not in turnover_data:
                    turnover_data[sku] = {
                        "name": sale_item.name,
                        "quantity_sold": 0,
                        "revenue": 0,
                        "current_stock": inventory_map[sku].quantity_on_hand
                    }

                turnover_data[sku]["quantity_sold"] += sale_item.quantity
                turnover_data[sku]["revenue"] += float(sale_item.total_price)

        # Calculate turnover rates
        for sku, data_item in turnover_data.items():
            avg_stock = (data_item["current_stock"] + data_item["quantity_sold"]) / 2
            if avg_stock > 0:
                # Annualized turnover rate (90 days of data)
                data_item["turnover_rate"] = (data_item["quantity_sold"] / avg_stock) * (365 / 90)
            else:
                data_item["turnover_rate"] = 0

        # Sort by turnover rate
        sorted_items = sorted(turnover_data.items(),
                            key=lambda x: x[1]["turnover_rate"],
                            reverse=True)

        return {
            "top_performers": dict(sorted_items[:10]),
            "slow_movers": dict(sorted_items[-10:]),
            "avg_turnover_rate": np.mean([item[1]["turnover_rate"] for item in sorted_items])
        }

    async def _analyze_seasonal_trends(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze seasonal sales trends"""
        sales = data["sales"]

        if not sales:
            return None

        # Group sales by month
        monthly_sales = {}
        for sale in sales:
            month = sale.sale_date.month
            if month not in monthly_sales:
                monthly_sales[month] = {
                    "count": 0,
                    "revenue": 0,
                    "avg_sale": 0
                }

            monthly_sales[month]["count"] += 1
            monthly_sales[month]["revenue"] += float(sale.total_amount)

        # Calculate averages
        for month_data in monthly_sales.values():
            if month_data["count"] > 0:
                month_data["avg_sale"] = month_data["revenue"] / month_data["count"]

        # Identify peak and low seasons
        if monthly_sales:
            peak_month = max(monthly_sales.items(), key=lambda x: x[1]["revenue"])
            low_month = min(monthly_sales.items(), key=lambda x: x[1]["revenue"])

            return {
                "monthly_breakdown": monthly_sales,
                "peak_season": {
                    "month": peak_month[0],
                    "revenue": peak_month[1]["revenue"]
                },
                "low_season": {
                    "month": low_month[0],
                    "revenue": low_month[1]["revenue"]
                }
            }

        return None

    async def _analyze_customer_patterns(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze customer behavior patterns"""
        sales = data["sales"]

        if not sales:
            return None

        # Group by customer
        customer_data = {}
        for sale in sales:
            customer = sale.customer_name or "Anonymous"
            if customer not in customer_data:
                customer_data[customer] = {
                    "purchase_count": 0,
                    "total_spent": 0,
                    "avg_purchase": 0,
                    "first_purchase": sale.sale_date,
                    "last_purchase": sale.sale_date
                }

            customer_data[customer]["purchase_count"] += 1
            customer_data[customer]["total_spent"] += float(sale.total_amount)

            if sale.sale_date < customer_data[customer]["first_purchase"]:
                customer_data[customer]["first_purchase"] = sale.sale_date
            if sale.sale_date > customer_data[customer]["last_purchase"]:
                customer_data[customer]["last_purchase"] = sale.sale_date

        # Calculate averages and segment customers
        for customer, cdata in customer_data.items():
            if cdata["purchase_count"] > 0:
                cdata["avg_purchase"] = cdata["total_spent"] / cdata["purchase_count"]

        # Customer segmentation
        repeat_customers = {k: v for k, v in customer_data.items() if v["purchase_count"] > 1}
        high_value_customers = {k: v for k, v in customer_data.items() if v["total_spent"] > 1000}

        return {
            "total_customers": len(customer_data),
            "repeat_customers": len(repeat_customers),
            "high_value_customers": len(high_value_customers),
            "avg_purchases_per_customer": np.mean([c["purchase_count"] for c in customer_data.values()]),
            "avg_customer_value": np.mean([c["total_spent"] for c in customer_data.values()])
        }

    async def _analyze_profitability(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze profitability across different dimensions"""
        sale_items = data["sale_items"]

        if not sale_items:
            return None

        # Profitability by product
        product_profit = {}
        for item in sale_items:
            sku = item.sku
            if sku not in product_profit:
                product_profit[sku] = {
                    "name": item.name,
                    "total_profit": 0,
                    "total_revenue": 0,
                    "quantity_sold": 0
                }

            product_profit[sku]["total_profit"] += item.profit
            product_profit[sku]["total_revenue"] += float(item.total_price)
            product_profit[sku]["quantity_sold"] += item.quantity

        # Calculate profit margins
        for sku, pdata in product_profit.items():
            if pdata["total_revenue"] > 0:
                pdata["profit_margin"] = (pdata["total_profit"] / pdata["total_revenue"]) * 100
            else:
                pdata["profit_margin"] = 0

        # Sort by profitability
        sorted_products = sorted(product_profit.items(),
                               key=lambda x: x[1]["total_profit"],
                               reverse=True)

        total_profit = sum(item.profit for item in sale_items)
        total_revenue = sum(float(item.total_price) for item in sale_items)
        overall_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0

        return {
            "overall_profit_margin": overall_margin,
            "total_profit": total_profit,
            "most_profitable_products": dict(sorted_products[:10]),
            "least_profitable_products": dict(sorted_products[-5:])
        }

    async def _update_cache(self, dealership_id: int, data: Dict[str, Any], insights: List[Dict[str, Any]]):
        """Update Redis cache with processed data and insights"""
        cache_key = f"dealership_insights:{dealership_id}"

        cache_data = {
            "dealership_id": dealership_id,
            "last_updated": datetime.utcnow().isoformat(),
            "inventory_count": len(data["inventory_items"]),
            "sales_count": len(data["sales"]),
            "insights": insights,
            "summary": {
                "total_inventory_value": sum(
                    float(item.retail_price or 0) * item.quantity_on_hand
                    for item in data["inventory_items"]
                ),
                "total_sales_90d": sum(
                    float(sale.total_amount) for sale in data["sales"]
                ),
                "low_stock_items": sum(
                    1 for item in data["inventory_items"] if item.is_low_stock
                )
            }
        }

        # Cache for 1 hour
        await redis_client.set(cache_key, cache_data, expire=3600)

        logger.info("Cache updated",
                   dealership_id=dealership_id,
                   insights_count=len(insights))
