import openai
from typing import Dict, Any, List, Optional
import json
import asyncio
from datetime import datetime
import structlog

from app.core.config import settings

logger = structlog.get_logger()


class AIService:
    """Service for AI-powered insights and recommendations"""
    
    def __init__(self):
        openai.api_key = settings.openai_api_key
        self.model = settings.openai_model
    
    async def generate_inventory_insights(self, item_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI insights for inventory items"""
        try:
            prompt = self._build_inventory_prompt(item_data, context)
            
            response = await self._call_openai(prompt, max_tokens=500)
            
            # Parse the response into structured insights
            insights = self._parse_inventory_insights(response, item_data)
            
            logger.info("Generated inventory insights", 
                       sku=item_data.get("sku"),
                       insights_count=len(insights))
            
            return insights
            
        except Exception as e:
            logger.error("Failed to generate inventory insights", 
                        sku=item_data.get("sku"),
                        error=str(e))
            return {"error": str(e), "generated_at": datetime.utcnow().isoformat()}
    
    async def generate_sales_insights(self, sale_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI insights for sales transactions"""
        try:
            prompt = self._build_sales_prompt(sale_data, context)
            
            response = await self._call_openai(prompt, max_tokens=400)
            
            # Parse the response into structured insights
            insights = self._parse_sales_insights(response, sale_data)
            
            logger.info("Generated sales insights", 
                       sale_id=sale_data.get("sale_id"),
                       insights_count=len(insights))
            
            return insights
            
        except Exception as e:
            logger.error("Failed to generate sales insights", 
                        sale_id=sale_data.get("sale_id"),
                        error=str(e))
            return {"error": str(e), "generated_at": datetime.utcnow().isoformat()}
    
    async def generate_demand_forecast(self, historical_data: List[Dict[str, Any]], item_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate demand forecasting for inventory items"""
        try:
            prompt = self._build_forecast_prompt(historical_data, item_info)
            
            response = await self._call_openai(prompt, max_tokens=600)
            
            # Parse forecast response
            forecast = self._parse_forecast_response(response, item_info)
            
            logger.info("Generated demand forecast", 
                       sku=item_info.get("sku"),
                       forecast_periods=len(forecast.get("periods", [])))
            
            return forecast
            
        except Exception as e:
            logger.error("Failed to generate demand forecast", 
                        sku=item_info.get("sku"),
                        error=str(e))
            return {"error": str(e), "generated_at": datetime.utcnow().isoformat()}
    
    async def generate_customer_insights(self, customer_data: Dict[str, Any], sales_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate insights about customer behavior"""
        try:
            prompt = self._build_customer_prompt(customer_data, sales_history)
            
            response = await self._call_openai(prompt, max_tokens=500)
            
            # Parse customer insights
            insights = self._parse_customer_insights(response, customer_data)
            
            logger.info("Generated customer insights", 
                       customer=customer_data.get("name", "Anonymous"))
            
            return insights
            
        except Exception as e:
            logger.error("Failed to generate customer insights", 
                        customer=customer_data.get("name", "Anonymous"),
                        error=str(e))
            return {"error": str(e), "generated_at": datetime.utcnow().isoformat()}
    
    async def generate_pricing_recommendations(self, item_data: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate pricing optimization recommendations"""
        try:
            prompt = self._build_pricing_prompt(item_data, market_data)
            
            response = await self._call_openai(prompt, max_tokens=400)
            
            # Parse pricing recommendations
            recommendations = self._parse_pricing_recommendations(response, item_data)
            
            logger.info("Generated pricing recommendations", 
                       sku=item_data.get("sku"))
            
            return recommendations
            
        except Exception as e:
            logger.error("Failed to generate pricing recommendations", 
                        sku=item_data.get("sku"),
                        error=str(e))
            return {"error": str(e), "generated_at": datetime.utcnow().isoformat()}
    
    async def _call_openai(self, prompt: str, max_tokens: int = 500) -> str:
        """Make async call to OpenAI API"""
        try:
            response = await asyncio.to_thread(
                openai.ChatCompletion.create,
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an AI assistant specialized in powersports dealership analytics and business intelligence. Provide actionable insights based on data."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error("OpenAI API call failed", error=str(e))
            raise e
    
    def _build_inventory_prompt(self, item_data: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Build prompt for inventory insights"""
        prompt = f"""
        Analyze this powersports inventory item and provide actionable insights:
        
        Item Details:
        - SKU: {item_data.get('sku')}
        - Name: {item_data.get('name')}
        - Brand: {item_data.get('brand', 'Unknown')}
        - Category: {item_data.get('category', 'Unknown')}
        - Current Stock: {item_data.get('quantity_on_hand', 0)}
        - Available: {item_data.get('quantity_available', 0)}
        - Retail Price: ${item_data.get('retail_price', 0):.2f}
        - Cost Price: ${item_data.get('cost_price', 0):.2f}
        - Margin: {item_data.get('margin', 0):.1f}%
        - Reorder Level: {item_data.get('reorder_level', 0)}
        - Last Sold: {item_data.get('last_sold', 'Never')}
        
        Context:
        - Total Inventory Items: {context.get('total_items', 0)}
        - Average Margin: {context.get('avg_margin', 0):.1f}%
        - Low Stock Items: {context.get('low_stock_count', 0)}
        
        Please provide:
        1. Stock status assessment (overstocked, understocked, optimal)
        2. Reorder recommendations with suggested quantities
        3. Pricing optimization suggestions
        4. Sales velocity insights
        5. Risk assessment (slow-moving, obsolete, etc.)
        
        Format as JSON with keys: stock_status, reorder_recommendation, pricing_suggestion, velocity_insight, risk_assessment
        """
        
        return prompt.strip()
    
    def _build_sales_prompt(self, sale_data: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Build prompt for sales insights"""
        items_summary = ", ".join([f"{item['name']} (${item['total_price']:.2f})" for item in sale_data.get('items', [])[:5]])
        
        prompt = f"""
        Analyze this powersports sale transaction and provide insights:
        
        Sale Details:
        - Sale ID: {sale_data.get('sale_id')}
        - Date: {sale_data.get('sale_date')}
        - Total Amount: ${sale_data.get('total_amount', 0):.2f}
        - Item Count: {sale_data.get('item_count', 0)}
        - Customer: {sale_data.get('customer_name', 'Anonymous')}
        - Payment Method: {sale_data.get('payment_method', 'Unknown')}
        - Items: {items_summary}
        
        Context:
        - Average Sale Amount: ${context.get('avg_sale_amount', 0):.2f}
        - Total Sales: {context.get('total_sales', 0)}
        - Average Profit Margin: {context.get('avg_profit_margin', 0):.1f}%
        
        Please provide:
        1. Sale performance assessment (above/below average)
        2. Customer behavior insights
        3. Cross-selling opportunities
        4. Seasonal patterns if applicable
        5. Follow-up recommendations
        
        Format as JSON with keys: performance_assessment, customer_insight, cross_sell_opportunities, seasonal_notes, follow_up_actions
        """
        
        return prompt.strip()
    
    def _build_forecast_prompt(self, historical_data: List[Dict[str, Any]], item_info: Dict[str, Any]) -> str:
        """Build prompt for demand forecasting"""
        data_summary = f"Historical sales over {len(historical_data)} periods"
        
        prompt = f"""
        Generate a demand forecast for this powersports item:
        
        Item: {item_info.get('name')} (SKU: {item_info.get('sku')})
        Category: {item_info.get('category', 'Unknown')}
        Current Stock: {item_info.get('current_stock', 0)}
        
        Historical Data: {data_summary}
        Recent trends, seasonality patterns, and sales velocity should be considered.
        
        Please provide:
        1. 30-day demand forecast
        2. 90-day demand forecast
        3. Seasonal adjustment factors
        4. Confidence level
        5. Recommended safety stock
        
        Format as JSON with keys: forecast_30d, forecast_90d, seasonal_factors, confidence_level, safety_stock_recommendation
        """
        
        return prompt.strip()
    
    def _build_customer_prompt(self, customer_data: Dict[str, Any], sales_history: List[Dict[str, Any]]) -> str:
        """Build prompt for customer insights"""
        prompt = f"""
        Analyze this powersports customer and provide insights:
        
        Customer: {customer_data.get('name', 'Anonymous')}
        Total Purchases: {customer_data.get('purchase_count', 0)}
        Total Spent: ${customer_data.get('total_spent', 0):.2f}
        Average Purchase: ${customer_data.get('avg_purchase', 0):.2f}
        
        Recent purchase patterns and preferences should be analyzed.
        
        Please provide:
        1. Customer segment classification
        2. Purchase behavior patterns
        3. Recommended products/services
        4. Retention strategies
        5. Lifetime value estimate
        
        Format as JSON with keys: segment, behavior_pattern, product_recommendations, retention_strategy, lifetime_value_estimate
        """
        
        return prompt.strip()
    
    def _build_pricing_prompt(self, item_data: Dict[str, Any], market_data: Dict[str, Any]) -> str:
        """Build prompt for pricing recommendations"""
        prompt = f"""
        Provide pricing optimization for this powersports item:
        
        Current Item:
        - Name: {item_data.get('name')}
        - Current Price: ${item_data.get('retail_price', 0):.2f}
        - Cost: ${item_data.get('cost_price', 0):.2f}
        - Current Margin: {item_data.get('margin', 0):.1f}%
        - Sales Velocity: {item_data.get('sales_velocity', 'Unknown')}
        
        Market Context:
        - Category Average Margin: {market_data.get('avg_category_margin', 0):.1f}%
        - Competitor Pricing: {market_data.get('competitor_range', 'Unknown')}
        
        Please provide:
        1. Optimal price recommendation
        2. Price elasticity assessment
        3. Competitive positioning
        4. Margin optimization strategy
        5. Dynamic pricing suggestions
        
        Format as JSON with keys: optimal_price, elasticity_assessment, competitive_position, margin_strategy, dynamic_pricing
        """
        
        return prompt.strip()

    def _parse_inventory_insights(self, response: str, item_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse AI response for inventory insights"""
        try:
            # Try to parse as JSON first
            insights = json.loads(response)
        except json.JSONDecodeError:
            # Fallback to text parsing
            insights = {
                "stock_status": "unknown",
                "reorder_recommendation": "Unable to parse AI response",
                "pricing_suggestion": "Review needed",
                "velocity_insight": "Analysis incomplete",
                "risk_assessment": "Manual review required"
            }

        # Add metadata
        insights.update({
            "generated_at": datetime.utcnow().isoformat(),
            "sku": item_data.get("sku"),
            "confidence": "medium",
            "ai_model": self.model
        })

        return insights

    def _parse_sales_insights(self, response: str, sale_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse AI response for sales insights"""
        try:
            insights = json.loads(response)
        except json.JSONDecodeError:
            insights = {
                "performance_assessment": "Unable to parse AI response",
                "customer_insight": "Analysis incomplete",
                "cross_sell_opportunities": [],
                "seasonal_notes": "Review needed",
                "follow_up_actions": []
            }

        insights.update({
            "generated_at": datetime.utcnow().isoformat(),
            "sale_id": sale_data.get("sale_id"),
            "confidence": "medium",
            "ai_model": self.model
        })

        return insights

    def _parse_forecast_response(self, response: str, item_info: Dict[str, Any]) -> Dict[str, Any]:
        """Parse AI response for demand forecast"""
        try:
            forecast = json.loads(response)
        except json.JSONDecodeError:
            forecast = {
                "forecast_30d": 0,
                "forecast_90d": 0,
                "seasonal_factors": {},
                "confidence_level": "low",
                "safety_stock_recommendation": 0
            }

        forecast.update({
            "generated_at": datetime.utcnow().isoformat(),
            "sku": item_info.get("sku"),
            "model_version": self.model,
            "forecast_type": "ai_generated"
        })

        return forecast

    def _parse_customer_insights(self, response: str, customer_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse AI response for customer insights"""
        try:
            insights = json.loads(response)
        except json.JSONDecodeError:
            insights = {
                "segment": "unknown",
                "behavior_pattern": "Analysis incomplete",
                "product_recommendations": [],
                "retention_strategy": "Manual review required",
                "lifetime_value_estimate": 0
            }

        insights.update({
            "generated_at": datetime.utcnow().isoformat(),
            "customer_name": customer_data.get("name"),
            "confidence": "medium",
            "ai_model": self.model
        })

        return insights

    def _parse_pricing_recommendations(self, response: str, item_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse AI response for pricing recommendations"""
        try:
            recommendations = json.loads(response)
        except json.JSONDecodeError:
            recommendations = {
                "optimal_price": item_data.get("retail_price", 0),
                "elasticity_assessment": "Analysis incomplete",
                "competitive_position": "Unknown",
                "margin_strategy": "Manual review required",
                "dynamic_pricing": "Not available"
            }

        recommendations.update({
            "generated_at": datetime.utcnow().isoformat(),
            "sku": item_data.get("sku"),
            "current_price": item_data.get("retail_price", 0),
            "confidence": "medium",
            "ai_model": self.model
        })

        return recommendations
