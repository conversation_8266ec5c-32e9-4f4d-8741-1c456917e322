from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from app.models.dealership import Dealership, DealershipUser, UserRole
from app.models.user import User
from app.schemas.dealership import DealershipCreate, DealershipUpdate


class DealershipService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_by_id(self, dealership_id: int) -> Optional[Dealership]:
        """Get dealership by ID"""
        result = await self.db.execute(
            select(Dealership)
            .options(selectinload(Dealership.user_associations))
            .where(Dealership.id == dealership_id)
        )
        return result.scalar_one_or_none()
    
    async def get_user_dealership(self, user_id: int, dealership_id: int) -> Optional[Dealership]:
        """Get dealership if user has access to it"""
        result = await self.db.execute(
            select(Dealership)
            .join(DealershipUser)
            .where(
                Dealership.id == dealership_id,
                DealershipUser.user_id == user_id,
                DealershipUser.is_active == True
            )
        )
        return result.scalar_one_or_none()
    
    async def get_user_dealerships(self, user_id: int) -> List[Dealership]:
        """Get all dealerships user has access to"""
        result = await self.db.execute(
            select(Dealership)
            .join(DealershipUser)
            .where(
                DealershipUser.user_id == user_id,
                DealershipUser.is_active == True
            )
            .order_by(Dealership.name)
        )
        return result.scalars().all()
    
    async def create(self, dealership_data: DealershipCreate, owner_user_id: int) -> Dealership:
        """Create a new dealership"""
        dealership = Dealership(
            name=dealership_data.name,
            business_name=dealership_data.business_name,
            address=dealership_data.address,
            city=dealership_data.city,
            state=dealership_data.state,
            zip_code=dealership_data.zip_code,
            country=dealership_data.country or "US",
            phone=dealership_data.phone,
            email=dealership_data.email,
            website=dealership_data.website,
            tax_id=dealership_data.tax_id,
            license_number=dealership_data.license_number
        )
        
        self.db.add(dealership)
        await self.db.flush()  # Get the ID
        
        # Add owner as dealership user
        dealership_user = DealershipUser(
            dealership_id=dealership.id,
            user_id=owner_user_id,
            role=UserRole.OWNER,
            is_active=True
        )
        
        self.db.add(dealership_user)
        await self.db.commit()
        await self.db.refresh(dealership)
        
        return dealership
    
    async def update(self, dealership_id: int, dealership_data: DealershipUpdate) -> Optional[Dealership]:
        """Update dealership information"""
        dealership = await self.get_by_id(dealership_id)
        if not dealership:
            return None
        
        update_data = dealership_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(dealership, field, value)
        
        await self.db.commit()
        await self.db.refresh(dealership)
        return dealership
    
    async def add_user_to_dealership(
        self, 
        dealership_id: int, 
        user_id: int, 
        role: UserRole = UserRole.VIEWER
    ) -> DealershipUser:
        """Add user to dealership with specified role"""
        # Check if association already exists
        existing = await self.db.execute(
            select(DealershipUser).where(
                DealershipUser.dealership_id == dealership_id,
                DealershipUser.user_id == user_id
            )
        )
        existing_association = existing.scalar_one_or_none()
        
        if existing_association:
            # Update existing association
            existing_association.role = role
            existing_association.is_active = True
            await self.db.commit()
            return existing_association
        
        # Create new association
        dealership_user = DealershipUser(
            dealership_id=dealership_id,
            user_id=user_id,
            role=role,
            is_active=True
        )
        
        self.db.add(dealership_user)
        await self.db.commit()
        await self.db.refresh(dealership_user)
        
        return dealership_user
    
    async def remove_user_from_dealership(self, dealership_id: int, user_id: int) -> bool:
        """Remove user from dealership"""
        result = await self.db.execute(
            select(DealershipUser).where(
                DealershipUser.dealership_id == dealership_id,
                DealershipUser.user_id == user_id
            )
        )
        association = result.scalar_one_or_none()
        
        if association:
            association.is_active = False
            await self.db.commit()
            return True
        
        return False
    
    async def get_user_role(self, user_id: int, dealership_id: int) -> Optional[UserRole]:
        """Get user's role in dealership"""
        result = await self.db.execute(
            select(DealershipUser.role).where(
                DealershipUser.user_id == user_id,
                DealershipUser.dealership_id == dealership_id,
                DealershipUser.is_active == True
            )
        )
        role = result.scalar_one_or_none()
        return role
    
    async def user_can_manage_dealership(self, user_id: int, dealership_id: int) -> bool:
        """Check if user can manage dealership (owner or manager)"""
        role = await self.get_user_role(user_id, dealership_id)
        return role in [UserRole.OWNER, UserRole.MANAGER] if role else False
