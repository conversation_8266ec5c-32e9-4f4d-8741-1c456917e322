from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import structlog

from app.models.lightspeed import LightspeedIntegration
from app.models.sales import Sale, SaleItem, SaleStatus, PaymentMethod
from app.models.inventory import InventoryItem
from app.services.lightspeed_service import LightspeedService, LightspeedAPIError

logger = structlog.get_logger()


class SalesSyncService:
    """Service for syncing sales data from Lightspeed"""
    
    def __init__(self, db: AsyncSession, lightspeed_service: LightspeedService):
        self.db = db
        self.lightspeed_service = lightspeed_service
    
    async def sync_sales(self, integration: LightspeedIntegration, since: Optional[datetime] = None) -> Dict[str, int]:
        """Sync sales data from Lightspeed"""
        stats = {
            "processed": 0,
            "created": 0,
            "updated": 0,
            "failed": 0
        }
        
        try:
            # If no since date provided, sync last 30 days
            if not since:
                since = datetime.utcnow() - timedelta(days=30)
            
            page = 1
            has_more = True
            
            while has_more:
                # Fetch sales from Lightspeed
                response = await self.lightspeed_service.get_sales(integration, since, page)
                sales_data = response.get("data", [])
                
                for sale_data in sales_data:
                    try:
                        await self._process_sale(integration, sale_data, stats)
                    except Exception as e:
                        logger.error("Failed to process sale", 
                                   sale_id=sale_data.get("id"),
                                   error=str(e))
                        stats["failed"] += 1
                
                # Check if there are more pages
                has_more = len(sales_data) == 200  # Assuming full page means more data
                page += 1
                
                if page > 1000:  # Safety limit
                    break
            
            logger.info("Sales sync completed", 
                       dealership_id=integration.dealership_id,
                       stats=stats)
            
            return stats
            
        except Exception as e:
            logger.error("Sales sync failed", 
                        dealership_id=integration.dealership_id,
                        error=str(e))
            stats["failed"] += 1
            raise e
    
    async def _process_sale(self, integration: LightspeedIntegration, sale_data: Dict[str, Any], stats: Dict[str, int]):
        """Process a single sale"""
        lightspeed_id = str(sale_data.get("id"))
        
        # Check if sale already exists
        result = await self.db.execute(
            select(Sale).where(
                Sale.lightspeed_id == lightspeed_id,
                Sale.dealership_id == integration.dealership_id
            )
        )
        existing_sale = result.scalar_one_or_none()
        
        # Extract sale information
        sale_number = sale_data.get("receipt_number") or sale_data.get("invoice_number") or lightspeed_id
        
        # Parse sale date
        sale_date = datetime.utcnow()
        if sale_data.get("sale_date"):
            try:
                sale_date = datetime.fromisoformat(sale_data["sale_date"].replace("Z", "+00:00"))
            except:
                pass
        
        # Extract customer information
        customer_data = sale_data.get("customer", {})
        customer_name = None
        customer_email = None
        customer_phone = None
        
        if customer_data:
            customer_name = customer_data.get("name") or f"{customer_data.get('first_name', '')} {customer_data.get('last_name', '')}".strip()
            customer_email = customer_data.get("email")
            customer_phone = customer_data.get("phone")
        
        # Extract financial information
        subtotal = float(sale_data.get("total_price", 0))
        tax_amount = float(sale_data.get("total_tax", 0))
        total_amount = float(sale_data.get("total_price", 0))
        
        # Map status
        status = self._map_sale_status(sale_data.get("status", ""))
        
        # Extract payment method
        payment_method = self._map_payment_method(sale_data.get("payments", []))
        
        if existing_sale:
            # Update existing sale
            existing_sale.customer_name = customer_name
            existing_sale.customer_email = customer_email
            existing_sale.customer_phone = customer_phone
            existing_sale.subtotal = subtotal
            existing_sale.tax_amount = tax_amount
            existing_sale.total_amount = total_amount
            existing_sale.status = status
            existing_sale.payment_method = payment_method
            existing_sale.lightspeed_data = sale_data
            existing_sale.updated_at = datetime.utcnow()
            
            # Update sale items
            await self._sync_sale_items(existing_sale, sale_data.get("line_items", []))
            
            stats["updated"] += 1
        else:
            # Create new sale
            sale = Sale(
                dealership_id=integration.dealership_id,
                sale_number=sale_number,
                customer_name=customer_name,
                customer_email=customer_email,
                customer_phone=customer_phone,
                subtotal=subtotal,
                tax_amount=tax_amount,
                total_amount=total_amount,
                status=status,
                payment_method=payment_method,
                sale_date=sale_date,
                lightspeed_id=lightspeed_id,
                lightspeed_data=sale_data
            )
            
            self.db.add(sale)
            await self.db.flush()  # Get the ID
            
            # Add sale items
            await self._sync_sale_items(sale, sale_data.get("line_items", []))
            
            stats["created"] += 1
        
        stats["processed"] += 1
        
        # Commit every 25 sales to avoid large transactions
        if stats["processed"] % 25 == 0:
            await self.db.commit()
    
    async def _sync_sale_items(self, sale: Sale, line_items_data: List[Dict[str, Any]]):
        """Sync sale items for a sale"""
        # Clear existing items if updating
        if sale.id:
            # Delete existing sale items
            await self.db.execute(
                select(SaleItem).where(SaleItem.sale_id == sale.id)
            )
        
        for item_data in line_items_data:
            # Find corresponding inventory item
            inventory_item_id = None
            product_id = item_data.get("product_id")
            
            if product_id:
                result = await self.db.execute(
                    select(InventoryItem.id).where(
                        InventoryItem.lightspeed_id == str(product_id),
                        InventoryItem.dealership_id == sale.dealership_id
                    )
                )
                inventory_item_id = result.scalar_one_or_none()
            
            # Extract item information
            sku = item_data.get("sku") or f"LS-{product_id}" if product_id else "UNKNOWN"
            name = item_data.get("name", "Unknown Item")
            quantity = int(item_data.get("quantity", 1))
            unit_price = float(item_data.get("price", 0))
            total_price = float(item_data.get("total", unit_price * quantity))
            unit_cost = float(item_data.get("cost", 0)) if item_data.get("cost") else None
            
            sale_item = SaleItem(
                sale_id=sale.id,
                inventory_item_id=inventory_item_id,
                sku=sku,
                name=name,
                quantity=quantity,
                unit_price=unit_price,
                total_price=total_price,
                unit_cost=unit_cost,
                lightspeed_id=str(item_data.get("id")) if item_data.get("id") else None
            )
            
            self.db.add(sale_item)
    
    def _map_sale_status(self, lightspeed_status: str) -> SaleStatus:
        """Map Lightspeed sale status to our enum"""
        status_mapping = {
            "CLOSED": SaleStatus.COMPLETED,
            "OPEN": SaleStatus.PENDING,
            "CANCELLED": SaleStatus.CANCELLED,
            "RETURNED": SaleStatus.REFUNDED,
            "LAYBY": SaleStatus.PENDING,
            "ACCOUNT": SaleStatus.PENDING,
            "ONACCOUNT": SaleStatus.PENDING
        }
        
        return status_mapping.get(lightspeed_status.upper(), SaleStatus.PENDING)
    
    def _map_payment_method(self, payments_data: List[Dict[str, Any]]) -> Optional[PaymentMethod]:
        """Map Lightspeed payment method to our enum"""
        if not payments_data:
            return None
        
        # Use the first payment method
        payment = payments_data[0]
        payment_type = payment.get("payment_type", {}).get("name", "").upper()
        
        method_mapping = {
            "CASH": PaymentMethod.CASH,
            "CREDIT": PaymentMethod.CREDIT_CARD,
            "CREDIT CARD": PaymentMethod.CREDIT_CARD,
            "DEBIT": PaymentMethod.DEBIT_CARD,
            "DEBIT CARD": PaymentMethod.DEBIT_CARD,
            "CHECK": PaymentMethod.CHECK,
            "CHEQUE": PaymentMethod.CHECK,
            "FINANCE": PaymentMethod.FINANCING,
            "FINANCING": PaymentMethod.FINANCING,
            "TRADE": PaymentMethod.TRADE_IN,
            "TRADE IN": PaymentMethod.TRADE_IN
        }
        
        return method_mapping.get(payment_type, PaymentMethod.OTHER)
    
    async def sync_single_sale(self, integration: LightspeedIntegration, sale_id: str) -> bool:
        """Sync a single sale by ID"""
        try:
            response = await self.lightspeed_service.make_api_request(
                integration,
                f"sales/{sale_id}"
            )
            
            sale_data = response.get("data")
            if not sale_data:
                return False
            
            stats = {"processed": 0, "created": 0, "updated": 0, "failed": 0}
            await self._process_sale(integration, sale_data, stats)
            await self.db.commit()
            
            return True
            
        except Exception as e:
            logger.error("Failed to sync single sale", 
                        sale_id=sale_id,
                        error=str(e))
            return False
