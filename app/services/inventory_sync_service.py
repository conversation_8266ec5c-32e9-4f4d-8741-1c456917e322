from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
import structlog

from app.models.lightspeed import LightspeedIntegration
from app.models.inventory import InventoryItem, Category
from app.models.dealership import Dealership
from app.services.lightspeed_service import LightspeedService, LightspeedAPIError

logger = structlog.get_logger()


class InventorySyncService:
    """Service for syncing inventory data from Lightspeed"""
    
    def __init__(self, db: AsyncSession, lightspeed_service: LightspeedService):
        self.db = db
        self.lightspeed_service = lightspeed_service
    
    async def sync_inventory(self, integration: LightspeedIntegration) -> Dict[str, int]:
        """Sync inventory data from Lightspeed"""
        stats = {
            "processed": 0,
            "created": 0,
            "updated": 0,
            "failed": 0
        }
        
        try:
            # Sync categories first
            await self._sync_categories(integration, stats)
            
            # Sync products/inventory
            await self._sync_products(integration, stats)
            
            logger.info("Inventory sync completed", 
                       dealership_id=integration.dealership_id,
                       stats=stats)
            
            return stats
            
        except Exception as e:
            logger.error("Inventory sync failed", 
                        dealership_id=integration.dealership_id,
                        error=str(e))
            stats["failed"] += 1
            raise e
    
    async def _sync_categories(self, integration: LightspeedIntegration, stats: Dict[str, int]):
        """Sync product categories from Lightspeed"""
        try:
            page = 1
            has_more = True
            
            while has_more:
                # Fetch categories from Lightspeed
                response = await self.lightspeed_service.make_api_request(
                    integration, 
                    "product_categories",
                    params={"page": page, "page_size": 200}
                )
                
                categories_data = response.get("data", [])
                
                for category_data in categories_data:
                    try:
                        await self._process_category(integration, category_data, stats)
                    except Exception as e:
                        logger.error("Failed to process category", 
                                   category_id=category_data.get("id"),
                                   error=str(e))
                        stats["failed"] += 1
                
                # Check if there are more pages
                pagination = response.get("version", {})
                has_more = len(categories_data) == 200  # Assuming full page means more data
                page += 1
                
                if page > 100:  # Safety limit
                    break
                    
        except LightspeedAPIError as e:
            logger.error("Failed to fetch categories", error=str(e))
            raise e
    
    async def _process_category(self, integration: LightspeedIntegration, category_data: Dict[str, Any], stats: Dict[str, int]):
        """Process a single category"""
        lightspeed_id = str(category_data.get("id"))
        
        # Check if category already exists
        result = await self.db.execute(
            select(Category).where(Category.lightspeed_id == lightspeed_id)
        )
        existing_category = result.scalar_one_or_none()
        
        if existing_category:
            # Update existing category
            existing_category.name = category_data.get("name", existing_category.name)
            existing_category.description = category_data.get("description")
            existing_category.updated_at = datetime.utcnow()
            stats["updated"] += 1
        else:
            # Create new category
            category = Category(
                name=category_data.get("name", "Unknown Category"),
                description=category_data.get("description"),
                lightspeed_id=lightspeed_id
            )
            self.db.add(category)
            stats["created"] += 1
        
        stats["processed"] += 1
        await self.db.commit()
    
    async def _sync_products(self, integration: LightspeedIntegration, stats: Dict[str, int]):
        """Sync products/inventory from Lightspeed"""
        try:
            page = 1
            has_more = True
            
            while has_more:
                # Fetch products from Lightspeed
                response = await self.lightspeed_service.get_products(integration, page)
                products_data = response.get("data", [])
                
                for product_data in products_data:
                    try:
                        await self._process_product(integration, product_data, stats)
                    except Exception as e:
                        logger.error("Failed to process product", 
                                   product_id=product_data.get("id"),
                                   error=str(e))
                        stats["failed"] += 1
                
                # Check if there are more pages
                has_more = len(products_data) == 200  # Assuming full page means more data
                page += 1
                
                if page > 500:  # Safety limit
                    break
                    
        except LightspeedAPIError as e:
            logger.error("Failed to fetch products", error=str(e))
            raise e
    
    async def _process_product(self, integration: LightspeedIntegration, product_data: Dict[str, Any], stats: Dict[str, int]):
        """Process a single product"""
        lightspeed_id = str(product_data.get("id"))
        
        # Check if product already exists
        result = await self.db.execute(
            select(InventoryItem).where(
                InventoryItem.lightspeed_id == lightspeed_id,
                InventoryItem.dealership_id == integration.dealership_id
            )
        )
        existing_item = result.scalar_one_or_none()
        
        # Extract product information
        name = product_data.get("name", "Unknown Product")
        sku = product_data.get("sku") or product_data.get("handle")
        description = product_data.get("description")
        brand = product_data.get("brand_name")
        
        # Extract pricing
        retail_price = None
        cost_price = None
        
        if "retail_price" in product_data:
            retail_price = float(product_data["retail_price"])
        
        if "supply_price" in product_data:
            cost_price = float(product_data["supply_price"])
        
        # Extract inventory data
        inventory_data = product_data.get("inventory", [])
        quantity_on_hand = 0
        quantity_available = 0
        
        if inventory_data:
            # Sum inventory across all outlets
            for inv in inventory_data:
                quantity_on_hand += float(inv.get("count", 0))
                quantity_available += float(inv.get("available_count", inv.get("count", 0)))
        
        # Find category
        category_id = None
        if product_data.get("product_type_id"):
            category_result = await self.db.execute(
                select(Category.id).where(
                    Category.lightspeed_id == str(product_data["product_type_id"])
                )
            )
            category_id = category_result.scalar_one_or_none()
        
        if existing_item:
            # Update existing item
            existing_item.name = name
            existing_item.description = description
            existing_item.brand = brand
            existing_item.retail_price = retail_price
            existing_item.cost_price = cost_price
            existing_item.quantity_on_hand = quantity_on_hand
            existing_item.quantity_available = quantity_available
            existing_item.category_id = category_id
            existing_item.lightspeed_data = product_data
            existing_item.updated_at = datetime.utcnow()
            stats["updated"] += 1
        else:
            # Create new item
            inventory_item = InventoryItem(
                dealership_id=integration.dealership_id,
                category_id=category_id,
                sku=sku or f"LS-{lightspeed_id}",
                name=name,
                description=description,
                brand=brand,
                retail_price=retail_price,
                cost_price=cost_price,
                quantity_on_hand=quantity_on_hand,
                quantity_available=quantity_available,
                lightspeed_id=lightspeed_id,
                lightspeed_data=product_data
            )
            self.db.add(inventory_item)
            stats["created"] += 1
        
        stats["processed"] += 1
        
        # Commit every 50 items to avoid large transactions
        if stats["processed"] % 50 == 0:
            await self.db.commit()
    
    async def sync_single_product(self, integration: LightspeedIntegration, product_id: str) -> bool:
        """Sync a single product by ID"""
        try:
            response = await self.lightspeed_service.make_api_request(
                integration,
                f"products/{product_id}"
            )
            
            product_data = response.get("data")
            if not product_data:
                return False
            
            stats = {"processed": 0, "created": 0, "updated": 0, "failed": 0}
            await self._process_product(integration, product_data, stats)
            await self.db.commit()
            
            return True
            
        except Exception as e:
            logger.error("Failed to sync single product", 
                        product_id=product_id,
                        error=str(e))
            return False
