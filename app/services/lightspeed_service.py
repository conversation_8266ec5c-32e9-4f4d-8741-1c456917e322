import httpx
import asyncio
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import structlog

from app.core.config import settings
from app.core.redis import redis_client
from app.models.lightspeed import LightspeedIntegration, SyncLog, SyncStatus, SyncType
from app.models.dealership import Dealership
from app.schemas.lightspeed import LightspeedAuthResponse, LightspeedTokenRefresh

logger = structlog.get_logger()


class LightspeedAPIError(Exception):
    """Custom exception for Lightspeed API errors"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)


class LightspeedService:
    """Service for interacting with Lightspeed Retail API"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.base_url = settings.lightspeed_api_base_url
        self.client_id = settings.lightspeed_client_id
        self.client_secret = settings.lightspeed_client_secret
        self.redirect_uri = settings.lightspeed_redirect_uri
    
    def get_authorization_url(self, state: str) -> str:
        """Generate OAuth authorization URL"""
        params = {
            "response_type": "code",
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "state": state
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"https://secure.retail.lightspeed.app/connect?{query_string}"
    
    async def exchange_code_for_token(self, code: str, domain_prefix: str) -> Dict[str, Any]:
        """Exchange authorization code for access token"""
        token_url = f"https://{domain_prefix}.retail.lightspeed.app/api/1.0/token"
        
        data = {
            "code": code,
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "grant_type": "authorization_code",
            "redirect_uri": self.redirect_uri
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    token_url,
                    data=data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error("Failed to exchange code for token", error=str(e))
                raise LightspeedAPIError(f"Token exchange failed: {str(e)}")
    
    async def refresh_access_token(self, integration: LightspeedIntegration) -> Dict[str, Any]:
        """Refresh access token using refresh token"""
        if not integration.refresh_token:
            raise LightspeedAPIError("No refresh token available")
        
        token_url = f"https://{integration.shop_id}.retail.lightspeed.app/api/1.0/token"
        
        data = {
            "refresh_token": integration.refresh_token,
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "grant_type": "refresh_token"
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    token_url,
                    data=data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                response.raise_for_status()
                token_data = response.json()
                
                # Update integration with new tokens
                integration.access_token = token_data["access_token"]
                integration.refresh_token = token_data["refresh_token"]
                integration.token_expires_at = datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
                
                await self.db.commit()
                return token_data
                
            except httpx.HTTPError as e:
                logger.error("Failed to refresh token", integration_id=integration.id, error=str(e))
                raise LightspeedAPIError(f"Token refresh failed: {str(e)}")
    
    async def make_api_request(
        self, 
        integration: LightspeedIntegration, 
        endpoint: str, 
        method: str = "GET",
        data: Optional[Dict] = None,
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Make authenticated API request to Lightspeed"""
        
        # Check if token needs refresh
        if integration.is_token_expired:
            await self.refresh_access_token(integration)
        
        url = f"https://{integration.shop_id}.retail.lightspeed.app/api/2.0/{endpoint}"
        headers = {
            "Authorization": f"Bearer {integration.access_token}",
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, json=data)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=headers, json=data)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=headers)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                # Handle rate limiting
                if response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 60))
                    logger.warning("Rate limited, waiting", retry_after=retry_after)
                    await asyncio.sleep(retry_after)
                    return await self.make_api_request(integration, endpoint, method, data, params)
                
                response.raise_for_status()
                return response.json()
                
            except httpx.HTTPError as e:
                logger.error("API request failed", endpoint=endpoint, error=str(e))
                raise LightspeedAPIError(f"API request failed: {str(e)}", response.status_code if hasattr(e, 'response') else None)
    
    async def get_products(self, integration: LightspeedIntegration, page: int = 1, page_size: int = 200) -> Dict[str, Any]:
        """Fetch products from Lightspeed"""
        params = {
            "page": page,
            "page_size": page_size
        }
        return await self.make_api_request(integration, "products", params=params)
    
    async def get_sales(self, integration: LightspeedIntegration, since: Optional[datetime] = None, page: int = 1) -> Dict[str, Any]:
        """Fetch sales from Lightspeed"""
        params = {
            "page": page,
            "page_size": 200
        }
        
        if since:
            params["since"] = since.isoformat()
        
        return await self.make_api_request(integration, "sales", params=params)
    
    async def get_customers(self, integration: LightspeedIntegration, page: int = 1) -> Dict[str, Any]:
        """Fetch customers from Lightspeed"""
        params = {
            "page": page,
            "page_size": 200
        }
        return await self.make_api_request(integration, "customers", params=params)
    
    async def get_inventory(self, integration: LightspeedIntegration, page: int = 1) -> Dict[str, Any]:
        """Fetch inventory records from Lightspeed"""
        params = {
            "page": page,
            "page_size": 200
        }
        return await self.make_api_request(integration, "inventory", params=params)
    
    async def create_integration(self, dealership_id: int, auth_data: Dict[str, Any]) -> LightspeedIntegration:
        """Create new Lightspeed integration"""
        integration = LightspeedIntegration(
            dealership_id=dealership_id,
            access_token=auth_data["access_token"],
            refresh_token=auth_data["refresh_token"],
            token_expires_at=datetime.utcnow() + timedelta(seconds=auth_data["expires_in"]),
            shop_id=auth_data["domain_prefix"],
            is_active=True,
            auto_sync_enabled=True
        )
        
        self.db.add(integration)
        await self.db.commit()
        await self.db.refresh(integration)
        
        logger.info("Lightspeed integration created", dealership_id=dealership_id, integration_id=integration.id)
        return integration
    
    async def get_integration_by_dealership(self, dealership_id: int) -> Optional[LightspeedIntegration]:
        """Get Lightspeed integration for dealership"""
        result = await self.db.execute(
            select(LightspeedIntegration).where(LightspeedIntegration.dealership_id == dealership_id)
        )
        return result.scalar_one_or_none()
    
    async def test_connection(self, integration: LightspeedIntegration) -> bool:
        """Test API connection"""
        try:
            await self.make_api_request(integration, "products", params={"page": 1, "page_size": 1})
            return True
        except LightspeedAPIError:
            return False
