from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from app.core.database import get_db
from app.api.v1.endpoints.auth import get_current_user
from app.schemas.user import User
from app.services.dealership_service import DealershipService
from app.services.data_pipeline_service import DataPipelineService
from app.tasks.ai_tasks import process_dealership_data, generate_inventory_insights, generate_sales_insights
from app.core.redis import redis_client
from app.models.inventory import InventoryItem
from app.models.sales import Sale
import structlog

logger = structlog.get_logger()
router = APIRouter()


@router.post("/process/{dealership_id}")
async def trigger_data_processing(
    dealership_id: int,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Trigger data processing pipeline for a dealership"""
    # Verify user has access to dealership
    dealership_service = DealershipService(db)
    dealership = await dealership_service.get_user_dealership(current_user.id, dealership_id)
    
    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found or access denied"
        )
    
    # Check if processing is already running
    processing_key = f"processing:{dealership_id}"
    is_processing = await redis_client.exists(processing_key)
    
    if is_processing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Data processing is already in progress for this dealership"
        )
    
    # Set processing flag
    await redis_client.set(processing_key, "true", expire=3600)  # 1 hour timeout
    
    # Trigger background processing
    task = process_dealership_data.delay(dealership_id)
    
    logger.info("Data processing triggered", 
               dealership_id=dealership_id,
               task_id=task.id,
               user_id=current_user.id)
    
    return {
        "message": "Data processing started",
        "task_id": task.id,
        "dealership_id": dealership_id
    }


@router.get("/insights/{dealership_id}")
async def get_dealership_insights(
    dealership_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get cached insights for a dealership"""
    # Verify user has access to dealership
    dealership_service = DealershipService(db)
    dealership = await dealership_service.get_user_dealership(current_user.id, dealership_id)
    
    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found or access denied"
        )
    
    # Get cached insights
    cache_key = f"dealership_insights:{dealership_id}"
    insights = await redis_client.get(cache_key)
    
    if not insights:
        # No cached insights, suggest running processing
        return {
            "message": "No insights available. Run data processing first.",
            "has_insights": False,
            "dealership_id": dealership_id
        }
    
    return {
        "has_insights": True,
        "dealership_id": dealership_id,
        "insights": insights
    }


@router.get("/inventory-insights/{dealership_id}")
async def get_inventory_insights(
    dealership_id: int,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    low_stock_only: bool = Query(False),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get AI insights for inventory items"""
    # Verify user has access to dealership
    dealership_service = DealershipService(db)
    dealership = await dealership_service.get_user_dealership(current_user.id, dealership_id)
    
    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found or access denied"
        )
    
    # Build query
    query = select(InventoryItem).where(
        InventoryItem.dealership_id == dealership_id,
        InventoryItem.is_active == True,
        InventoryItem.ai_insights.isnot(None)
    )
    
    if category:
        from app.models.inventory import Category
        query = query.join(Category).where(Category.name.ilike(f"%{category}%"))
    
    if low_stock_only:
        query = query.where(InventoryItem.quantity_available <= InventoryItem.reorder_level)
    
    # Apply pagination
    offset = (page - 1) * page_size
    query = query.offset(offset).limit(page_size)
    
    # Execute query
    result = await db.execute(query)
    items = result.scalars().all()
    
    # Get total count
    count_query = select(func.count(InventoryItem.id)).where(
        InventoryItem.dealership_id == dealership_id,
        InventoryItem.is_active == True,
        InventoryItem.ai_insights.isnot(None)
    )
    
    if category:
        count_query = count_query.join(Category).where(Category.name.ilike(f"%{category}%"))
    
    if low_stock_only:
        count_query = count_query.where(InventoryItem.quantity_available <= InventoryItem.reorder_level)
    
    count_result = await db.execute(count_query)
    total = count_result.scalar()
    
    # Format response
    inventory_insights = []
    for item in items:
        insights = item.ai_insights or {}
        inventory_insights.append({
            "id": item.id,
            "sku": item.sku,
            "name": item.name,
            "brand": item.brand,
            "category": item.category.name if item.category else None,
            "quantity_on_hand": item.quantity_on_hand,
            "quantity_available": item.quantity_available,
            "retail_price": float(item.retail_price) if item.retail_price else None,
            "margin": item.margin,
            "is_low_stock": item.is_low_stock,
            "ai_insights": insights
        })
    
    return {
        "items": inventory_insights,
        "total": total,
        "page": page,
        "page_size": page_size,
        "has_more": total > (page * page_size)
    }


@router.get("/sales-insights/{dealership_id}")
async def get_sales_insights(
    dealership_id: int,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get AI insights for sales transactions"""
    # Verify user has access to dealership
    dealership_service = DealershipService(db)
    dealership = await dealership_service.get_user_dealership(current_user.id, dealership_id)
    
    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found or access denied"
        )
    
    # Build query for recent sales with AI insights
    since_date = datetime.utcnow() - timedelta(days=days)
    
    query = select(Sale).where(
        Sale.dealership_id == dealership_id,
        Sale.sale_date >= since_date,
        Sale.ai_insights.isnot(None)
    ).order_by(Sale.sale_date.desc())
    
    # Apply pagination
    offset = (page - 1) * page_size
    query = query.offset(offset).limit(page_size)
    
    # Execute query
    result = await db.execute(query)
    sales = result.scalars().all()
    
    # Get total count
    count_query = select(func.count(Sale.id)).where(
        Sale.dealership_id == dealership_id,
        Sale.sale_date >= since_date,
        Sale.ai_insights.isnot(None)
    )
    
    count_result = await db.execute(count_query)
    total = count_result.scalar()
    
    # Format response
    sales_insights = []
    for sale in sales:
        insights = sale.ai_insights or {}
        sales_insights.append({
            "id": sale.id,
            "sale_number": sale.sale_number,
            "sale_date": sale.sale_date.isoformat(),
            "total_amount": float(sale.total_amount),
            "item_count": sale.item_count,
            "customer_name": sale.customer_name,
            "payment_method": sale.payment_method.value if sale.payment_method else None,
            "status": sale.status.value,
            "ai_insights": insights
        })
    
    return {
        "sales": sales_insights,
        "total": total,
        "page": page,
        "page_size": page_size,
        "days": days,
        "has_more": total > (page * page_size)
    }


@router.post("/generate-insights/{dealership_id}")
async def trigger_insight_generation(
    dealership_id: int,
    insight_type: str = Query(..., regex="^(inventory|sales|all)$"),
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Trigger AI insight generation for specific data types"""
    # Verify user has access to dealership
    dealership_service = DealershipService(db)
    dealership = await dealership_service.get_user_dealership(current_user.id, dealership_id)
    
    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found or access denied"
        )
    
    tasks = []
    
    if insight_type in ["inventory", "all"]:
        task = generate_inventory_insights.delay(dealership_id)
        tasks.append({"type": "inventory", "task_id": task.id})
    
    if insight_type in ["sales", "all"]:
        task = generate_sales_insights.delay(dealership_id)
        tasks.append({"type": "sales", "task_id": task.id})
    
    logger.info("AI insight generation triggered", 
               dealership_id=dealership_id,
               insight_type=insight_type,
               task_count=len(tasks),
               user_id=current_user.id)
    
    return {
        "message": f"AI insight generation started for {insight_type}",
        "dealership_id": dealership_id,
        "tasks": tasks
    }


@router.get("/dashboard/{dealership_id}")
async def get_dashboard_data(
    dealership_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive dashboard data for a dealership"""
    # Verify user has access to dealership
    dealership_service = DealershipService(db)
    dealership = await dealership_service.get_user_dealership(current_user.id, dealership_id)
    
    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found or access denied"
        )
    
    # Get cached insights
    cache_key = f"dealership_insights:{dealership_id}"
    cached_insights = await redis_client.get(cache_key)
    
    # Get basic stats
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    
    # Inventory stats
    inventory_stats = await db.execute(
        select(
            func.count(InventoryItem.id).label("total_items"),
            func.sum(InventoryItem.quantity_on_hand * InventoryItem.retail_price).label("total_value"),
            func.count().filter(InventoryItem.quantity_available <= InventoryItem.reorder_level).label("low_stock_count")
        ).where(
            InventoryItem.dealership_id == dealership_id,
            InventoryItem.is_active == True
        )
    )
    inventory_row = inventory_stats.first()
    
    # Sales stats
    sales_stats = await db.execute(
        select(
            func.count(Sale.id).label("total_sales"),
            func.sum(Sale.total_amount).label("total_revenue"),
            func.avg(Sale.total_amount).label("avg_sale_amount")
        ).where(
            Sale.dealership_id == dealership_id,
            Sale.sale_date >= thirty_days_ago
        )
    )
    sales_row = sales_stats.first()
    
    dashboard_data = {
        "dealership_id": dealership_id,
        "last_updated": datetime.utcnow().isoformat(),
        "inventory": {
            "total_items": inventory_row.total_items or 0,
            "total_value": float(inventory_row.total_value or 0),
            "low_stock_count": inventory_row.low_stock_count or 0
        },
        "sales_30d": {
            "total_sales": sales_row.total_sales or 0,
            "total_revenue": float(sales_row.total_revenue or 0),
            "avg_sale_amount": float(sales_row.avg_sale_amount or 0)
        },
        "insights": cached_insights.get("insights", []) if cached_insights else [],
        "has_cached_insights": cached_insights is not None
    }
    
    return dashboard_data
