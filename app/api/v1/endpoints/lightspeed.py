import secrets
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func

from app.core.database import get_db
from app.api.v1.endpoints.auth import get_current_user
from app.schemas.user import User
from app.schemas.lightspeed import (
    LightspeedConnectionStatus,
    SyncRequest,
    SyncResponse,
    LightspeedAuthResponse
)
from app.services.lightspeed_service import LightspeedService, LightspeedAPIError
from app.services.dealership_service import DealershipService
from app.tasks.lightspeed_sync import sync_dealership_data
from app.core.redis import redis_client
from app.models.lightspeed import LightspeedIntegration, SyncLog
from app.models.inventory import InventoryItem
from app.models.sales import Sale, SaleStatus
import structlog

logger = structlog.get_logger()
router = APIRouter()


@router.get("/auth-url")
async def get_lightspeed_auth_url(
    dealership_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Lightspeed OAuth authorization URL"""
    # Verify user has access to dealership
    dealership_service = DealershipService(db)
    dealership = await dealership_service.get_user_dealership(current_user.id, dealership_id)

    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found or access denied"
        )

    # Generate secure state parameter
    state = secrets.token_urlsafe(32)

    # Store state in Redis with dealership info
    await redis_client.set(
        f"lightspeed_oauth_state:{state}",
        {"dealership_id": dealership_id, "user_id": current_user.id},
        expire=600  # 10 minutes
    )

    lightspeed_service = LightspeedService(db)
    auth_url = lightspeed_service.get_authorization_url(state)

    return {"auth_url": auth_url, "state": state}


@router.post("/callback")
async def lightspeed_oauth_callback(
    code: str,
    state: str,
    domain_prefix: str,
    db: AsyncSession = Depends(get_db)
):
    """Handle Lightspeed OAuth callback"""
    # Verify state parameter
    state_data = await redis_client.get(f"lightspeed_oauth_state:{state}")
    if not state_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired state parameter"
        )

    dealership_id = state_data["dealership_id"]
    user_id = state_data["user_id"]

    # Clean up state
    await redis_client.delete(f"lightspeed_oauth_state:{state}")

    try:
        lightspeed_service = LightspeedService(db)

        # Exchange code for tokens
        token_data = await lightspeed_service.exchange_code_for_token(code, domain_prefix)

        # Check if integration already exists
        existing_integration = await lightspeed_service.get_integration_by_dealership(dealership_id)

        if existing_integration:
            # Update existing integration
            existing_integration.access_token = token_data["access_token"]
            existing_integration.refresh_token = token_data["refresh_token"]
            existing_integration.token_expires_at = datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
            existing_integration.shop_id = domain_prefix
            existing_integration.is_active = True
            await db.commit()
            integration = existing_integration
        else:
            # Create new integration
            integration = await lightspeed_service.create_integration(dealership_id, token_data)

        # Test connection
        connection_test = await lightspeed_service.test_connection(integration)

        logger.info("Lightspeed integration completed",
                   dealership_id=dealership_id,
                   integration_id=integration.id,
                   connection_test=connection_test)

        return {
            "message": "Lightspeed integration successful",
            "integration_id": integration.id,
            "connection_test": connection_test
        }

    except LightspeedAPIError as e:
        logger.error("Lightspeed integration failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Integration failed: {e.message}"
        )


@router.get("/status/{dealership_id}", response_model=LightspeedConnectionStatus)
async def get_lightspeed_status(
    dealership_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Lightspeed connection status for dealership"""
    # Verify user has access to dealership
    dealership_service = DealershipService(db)
    dealership = await dealership_service.get_user_dealership(current_user.id, dealership_id)

    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found or access denied"
        )

    lightspeed_service = LightspeedService(db)
    integration = await lightspeed_service.get_integration_by_dealership(dealership_id)

    if not integration:
        return LightspeedConnectionStatus(is_connected=False)

    # Test connection
    connection_test = await lightspeed_service.test_connection(integration)

    return LightspeedConnectionStatus(
        is_connected=connection_test and integration.is_active,
        shop_name=integration.shop_name,
        last_sync=integration.last_successful_sync_at,
        sync_status=integration.last_sync_status.value if integration.last_sync_status else None,
        error_message=integration.last_sync_error if not connection_test else None
    )


@router.post("/sync/{dealership_id}", response_model=SyncResponse)
async def trigger_sync(
    dealership_id: int,
    sync_request: SyncRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Trigger manual sync for dealership"""
    # Verify user has access to dealership
    dealership_service = DealershipService(db)
    dealership = await dealership_service.get_user_dealership(current_user.id, dealership_id)

    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found or access denied"
        )

    lightspeed_service = LightspeedService(db)
    integration = await lightspeed_service.get_integration_by_dealership(dealership_id)

    if not integration or not integration.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Lightspeed integration not found or inactive"
        )

    # Trigger background sync task
    task = sync_dealership_data.delay(dealership_id)

    logger.info("Manual sync triggered",
               dealership_id=dealership_id,
               task_id=task.id,
               user_id=current_user.id)

    return SyncResponse(
        task_id=task.id,
        status="queued",
        message="Sync task has been queued for processing"
    )


@router.post("/webhook")
async def lightspeed_webhook(
    request: dict,
    db: AsyncSession = Depends(get_db)
):
    """Handle Lightspeed webhook events"""
    try:
        # Extract webhook data
        event_type = request.get("type")
        event_data = request.get("data", {})
        domain_prefix = request.get("domain_prefix")

        if not event_type or not domain_prefix:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid webhook payload"
            )

        # Find integration by domain prefix
        lightspeed_service = LightspeedService(db)
        result = await db.execute(
            select(LightspeedIntegration).where(
                LightspeedIntegration.shop_id == domain_prefix,
                LightspeedIntegration.is_active == True
            )
        )
        integration = result.scalar_one_or_none()

        if not integration:
            logger.warning("Webhook received for unknown integration", domain_prefix=domain_prefix)
            return {"status": "ignored", "reason": "integration not found"}

        # Process webhook based on event type
        await _process_webhook_event(db, integration, event_type, event_data)

        logger.info("Webhook processed",
                   event_type=event_type,
                   dealership_id=integration.dealership_id)

        return {"status": "processed"}

    except Exception as e:
        logger.error("Webhook processing failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Webhook processing failed"
        )


async def _process_webhook_event(
    db: AsyncSession,
    integration: LightspeedIntegration,
    event_type: str,
    event_data: Dict[str, Any]
):
    """Process individual webhook event"""
    lightspeed_service = LightspeedService(db)

    if event_type.startswith("product."):
        # Handle product events
        from app.services.inventory_sync_service import InventorySyncService
        inventory_service = InventorySyncService(db, lightspeed_service)

        product_id = event_data.get("id")
        if product_id:
            if event_type == "product.deleted":
                # Mark product as inactive
                await db.execute(
                    update(InventoryItem)
                    .where(
                        InventoryItem.lightspeed_id == str(product_id),
                        InventoryItem.dealership_id == integration.dealership_id
                    )
                    .values(is_active=False)
                )
            else:
                # Sync the updated/created product
                await inventory_service.sync_single_product(integration, str(product_id))

    elif event_type.startswith("sale."):
        # Handle sale events
        from app.services.sales_sync_service import SalesSyncService
        sales_service = SalesSyncService(db, lightspeed_service)

        sale_id = event_data.get("id")
        if sale_id:
            if event_type == "sale.deleted":
                # Mark sale as cancelled
                await db.execute(
                    update(Sale)
                    .where(
                        Sale.lightspeed_id == str(sale_id),
                        Sale.dealership_id == integration.dealership_id
                    )
                    .values(status=SaleStatus.CANCELLED)
                )
            else:
                # Sync the updated/created sale
                await sales_service.sync_single_sale(integration, str(sale_id))

    await db.commit()


@router.get("/sync-logs/{dealership_id}")
async def get_sync_logs(
    dealership_id: int,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get sync logs for dealership"""
    # Verify user has access to dealership
    dealership_service = DealershipService(db)
    dealership = await dealership_service.get_user_dealership(current_user.id, dealership_id)

    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found or access denied"
        )

    # Get integration
    lightspeed_service = LightspeedService(db)
    integration = await lightspeed_service.get_integration_by_dealership(dealership_id)

    if not integration:
        return {"logs": [], "total": 0, "page": page, "page_size": page_size}

    # Get sync logs
    offset = (page - 1) * page_size
    result = await db.execute(
        select(SyncLog)
        .where(SyncLog.integration_id == integration.id)
        .order_by(SyncLog.created_at.desc())
        .offset(offset)
        .limit(page_size)
    )
    logs = result.scalars().all()

    # Get total count
    count_result = await db.execute(
        select(func.count(SyncLog.id))
        .where(SyncLog.integration_id == integration.id)
    )
    total = count_result.scalar()

    return {
        "logs": [
            {
                "id": log.id,
                "sync_type": log.sync_type.value,
                "status": log.status.value,
                "started_at": log.started_at,
                "completed_at": log.completed_at,
                "duration_seconds": log.duration_seconds,
                "records_processed": log.records_processed,
                "records_created": log.records_created,
                "records_updated": log.records_updated,
                "records_failed": log.records_failed,
                "error_message": log.error_message
            }
            for log in logs
        ],
        "total": total,
        "page": page,
        "page_size": page_size
    }
