from fastapi import APIRouter
from app.api.v1.endpoints import auth, dealerships, inventory, sales, lightspeed, ai, users

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(dealerships.router, prefix="/dealerships", tags=["dealerships"])
api_router.include_router(inventory.router, prefix="/inventory", tags=["inventory"])
api_router.include_router(sales.router, prefix="/sales", tags=["sales"])
api_router.include_router(lightspeed.router, prefix="/lightspeed", tags=["lightspeed"])
api_router.include_router(ai.router, prefix="/ai", tags=["ai"])
