from typing import List, Optional
from pydantic import BaseSettings, validator
from functools import lru_cache
import os


class Settings(BaseSettings):
    # Application
    app_name: str = "DealDash Platform"
    app_version: str = "1.0.0"
    debug: bool = False
    environment: str = "production"
    
    # Database
    database_url: str
    database_test_url: Optional[str] = None
    
    # Redis
    redis_url: str = "redis://localhost:6379/0"
    
    # Security
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Lightspeed API
    lightspeed_client_id: str
    lightspeed_client_secret: str
    lightspeed_redirect_uri: str
    lightspeed_api_base_url: str = "https://api.lightspeedretail.com"
    
    # OpenAI
    openai_api_key: str
    openai_model: str = "gpt-4"
    
    # Celery
    celery_broker_url: str = "redis://localhost:6379/1"
    celery_result_backend: str = "redis://localhost:6379/2"
    
    # CORS
    allowed_origins: List[str] = ["http://localhost:3000"]
    
    # Rate Limiting
    rate_limit_per_minute: int = 100
    
    # File Upload
    max_file_size_mb: int = 10
    upload_dir: str = "./uploads"
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "json"
    
    @validator("allowed_origins", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    return Settings()


settings = get_settings()
