from celery import Celery
from app.core.config import settings

# Create Celery instance
celery_app = Celery(
    "dealdash",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=["app.tasks.lightspeed_sync", "app.tasks.ai_tasks"]
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Periodic tasks configuration
celery_app.conf.beat_schedule = {
    "sync-lightspeed-data": {
        "task": "app.tasks.lightspeed_sync.sync_all_dealerships",
        "schedule": 3600.0,  # Run every hour
    },
    "generate-ai-insights": {
        "task": "app.tasks.ai_tasks.generate_daily_insights",
        "schedule": 86400.0,  # Run daily
    },
}
