version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: dealdash_postgres
    environment:
      POSTGRES_DB: dealdash_db
      POSTGRES_USER: dealdash_user
      POSTGRES_PASSWORD: dealdash_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dealdash_user -d dealdash_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: dealdash_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Application
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dealdash_api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://dealdash_user:dealdash_password@postgres:5432/dealdash_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./app:/app/app
      - ./uploads:/app/uploads
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dealdash_celery_worker
    environment:
      - DATABASE_URL=postgresql+asyncpg://dealdash_user:dealdash_password@postgres:5432/dealdash_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./app:/app/app
    command: celery -A app.core.celery worker --loglevel=info

  # Celery Beat (Scheduler)
  celery_beat:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dealdash_celery_beat
    environment:
      - DATABASE_URL=postgresql+asyncpg://dealdash_user:dealdash_password@postgres:5432/dealdash_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./app:/app/app
    command: celery -A app.core.celery beat --loglevel=info

  # Flower (Celery Monitoring)
  flower:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dealdash_flower
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - redis
    command: celery -A app.core.celery flower --port=5555

volumes:
  postgres_data:
  redis_data:
