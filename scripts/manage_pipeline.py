#!/usr/bin/env python3
"""
Data Pipeline Management Script
This script helps manage and monitor the data processing pipeline
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from app.core.database import AsyncSessionLocal
from app.services.data_pipeline_service import DataPipelineService
from app.services.dealership_service import DealershipService
from app.models.dealership import Dealership
from app.models.lightspeed import LightspeedIntegration
from app.models.inventory import InventoryItem
from app.models.sales import Sale
from app.core.redis import redis_client
from app.tasks.ai_tasks import process_dealership_data


async def check_pipeline_status():
    """Check the status of data pipeline for all dealerships"""
    async with AsyncSessionLocal() as db:
        print("🔍 Checking Data Pipeline Status")
        print("=" * 50)
        
        # Get all active dealerships
        result = await db.execute(
            select(Dealership)
            .where(Dealership.is_active == True)
            .order_by(Dealership.name)
        )
        dealerships = result.scalars().all()
        
        if not dealerships:
            print("❌ No active dealerships found.")
            return
        
        print(f"📋 Found {len(dealerships)} active dealership(s):")
        print("-" * 80)
        
        for dealership in dealerships:
            # Check if Lightspeed integration exists
            integration_result = await db.execute(
                select(LightspeedIntegration)
                .where(LightspeedIntegration.dealership_id == dealership.id)
            )
            integration = integration_result.scalar_one_or_none()
            
            # Get data counts
            inventory_count = await db.execute(
                select(func.count(InventoryItem.id))
                .where(
                    InventoryItem.dealership_id == dealership.id,
                    InventoryItem.is_active == True
                )
            )
            inventory_total = inventory_count.scalar()
            
            # Count items with AI insights
            ai_inventory_count = await db.execute(
                select(func.count(InventoryItem.id))
                .where(
                    InventoryItem.dealership_id == dealership.id,
                    InventoryItem.is_active == True,
                    InventoryItem.ai_insights.isnot(None)
                )
            )
            ai_inventory_total = ai_inventory_count.scalar()
            
            # Get sales count (last 30 days)
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            sales_count = await db.execute(
                select(func.count(Sale.id))
                .where(
                    Sale.dealership_id == dealership.id,
                    Sale.sale_date >= thirty_days_ago
                )
            )
            sales_total = sales_count.scalar()
            
            # Count sales with AI insights
            ai_sales_count = await db.execute(
                select(func.count(Sale.id))
                .where(
                    Sale.dealership_id == dealership.id,
                    Sale.sale_date >= thirty_days_ago,
                    Sale.ai_insights.isnot(None)
                )
            )
            ai_sales_total = ai_sales_count.scalar()
            
            # Check cached insights
            cache_key = f"dealership_insights:{dealership.id}"
            cached_insights = await redis_client.get(cache_key)
            
            # Check if processing is running
            processing_key = f"processing:{dealership.id}"
            is_processing = await redis_client.exists(processing_key)
            
            # Display status
            integration_status = "✅ Connected" if integration and integration.is_active else "❌ Not Connected"
            processing_status = "🔄 Processing" if is_processing else "⏸️  Idle"
            cache_status = "✅ Fresh" if cached_insights else "❌ Stale"
            
            print(f"ID: {dealership.id:3d} | {dealership.name:25s}")
            print(f"     | Lightspeed: {integration_status:15s} | Status: {processing_status}")
            print(f"     | Inventory: {inventory_total:4d} items ({ai_inventory_total:3d} with AI)")
            print(f"     | Sales (30d): {sales_total:3d} sales ({ai_sales_total:3d} with AI)")
            print(f"     | Cache: {cache_status:15s}")
            print("-" * 80)


async def run_pipeline_for_dealership(dealership_id: int):
    """Run the data pipeline for a specific dealership"""
    async with AsyncSessionLocal() as db:
        # Verify dealership exists
        result = await db.execute(
            select(Dealership).where(Dealership.id == dealership_id)
        )
        dealership = result.scalar_one_or_none()
        
        if not dealership:
            print(f"❌ Dealership {dealership_id} not found.")
            return
        
        print(f"🚀 Running data pipeline for: {dealership.name}")
        print("-" * 50)
        
        # Check if already processing
        processing_key = f"processing:{dealership_id}"
        is_processing = await redis_client.exists(processing_key)
        
        if is_processing:
            print("⚠️  Pipeline is already running for this dealership.")
            return
        
        try:
            # Set processing flag
            await redis_client.set(processing_key, "true", expire=3600)
            
            # Run pipeline
            pipeline_service = DataPipelineService(db)
            start_time = datetime.utcnow()
            
            print("📊 Processing dealership data...")
            metrics = await pipeline_service.process_dealership_data(dealership_id)
            
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            print("✅ Pipeline completed successfully!")
            print(f"⏱️  Execution time: {duration:.2f} seconds")
            print(f"📈 Records processed: {metrics.records_processed}")
            print(f"🧠 Insights generated: {metrics.insights_generated}")
            print(f"❌ Errors: {metrics.errors}")
            
        except Exception as e:
            print(f"❌ Pipeline failed: {str(e)}")
        finally:
            # Clear processing flag
            await redis_client.delete(processing_key)


async def run_pipeline_for_all():
    """Run the data pipeline for all active dealerships"""
    async with AsyncSessionLocal() as db:
        print("🚀 Running data pipeline for all active dealerships")
        print("=" * 60)
        
        # Get all active dealerships with Lightspeed integration
        result = await db.execute(
            select(Dealership.id, Dealership.name)
            .join(LightspeedIntegration)
            .where(
                Dealership.is_active == True,
                LightspeedIntegration.is_active == True
            )
        )
        dealerships = result.fetchall()
        
        if not dealerships:
            print("❌ No dealerships with active Lightspeed integration found.")
            return
        
        print(f"📋 Processing {len(dealerships)} dealership(s)...")
        
        for dealership_id, dealership_name in dealerships:
            print(f"\n🔄 Processing: {dealership_name} (ID: {dealership_id})")
            await run_pipeline_for_dealership(dealership_id)


async def clear_cache(dealership_id: int = None):
    """Clear cached insights"""
    if dealership_id:
        cache_key = f"dealership_insights:{dealership_id}"
        await redis_client.delete(cache_key)
        print(f"🗑️  Cleared cache for dealership {dealership_id}")
    else:
        # Clear all dealership caches
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Dealership.id))
            dealership_ids = [row[0] for row in result.fetchall()]
            
            for did in dealership_ids:
                cache_key = f"dealership_insights:{did}"
                await redis_client.delete(cache_key)
            
            print(f"🗑️  Cleared cache for {len(dealership_ids)} dealerships")


async def view_insights(dealership_id: int):
    """View cached insights for a dealership"""
    cache_key = f"dealership_insights:{dealership_id}"
    insights = await redis_client.get(cache_key)
    
    if not insights:
        print(f"❌ No cached insights found for dealership {dealership_id}")
        return
    
    print(f"📊 Insights for Dealership {dealership_id}")
    print("=" * 50)
    print(f"Last Updated: {insights.get('last_updated', 'Unknown')}")
    print(f"Inventory Items: {insights.get('inventory_count', 0)}")
    print(f"Sales Records: {insights.get('sales_count', 0)}")
    
    summary = insights.get('summary', {})
    print(f"Total Inventory Value: ${summary.get('total_inventory_value', 0):,.2f}")
    print(f"Total Sales (90d): ${summary.get('total_sales_90d', 0):,.2f}")
    print(f"Low Stock Items: {summary.get('low_stock_items', 0)}")
    
    insights_list = insights.get('insights', [])
    print(f"\nGenerated Insights: {len(insights_list)}")
    for insight in insights_list:
        print(f"  - {insight.get('type', 'Unknown')}: Generated at {insight.get('generated_at', 'Unknown')}")


async def monitor_processing():
    """Monitor active processing tasks"""
    async with AsyncSessionLocal() as db:
        print("🔍 Monitoring Active Processing Tasks")
        print("=" * 50)
        
        # Get all dealerships
        result = await db.execute(select(Dealership.id, Dealership.name))
        dealerships = result.fetchall()
        
        active_tasks = []
        for dealership_id, dealership_name in dealerships:
            processing_key = f"processing:{dealership_id}"
            is_processing = await redis_client.exists(processing_key)
            
            if is_processing:
                active_tasks.append((dealership_id, dealership_name))
        
        if not active_tasks:
            print("✅ No active processing tasks found.")
        else:
            print(f"🔄 Found {len(active_tasks)} active processing task(s):")
            for dealership_id, dealership_name in active_tasks:
                print(f"  - {dealership_name} (ID: {dealership_id})")


async def main():
    """Main management function"""
    print("🤖 DealDash Data Pipeline Manager")
    print("=" * 50)
    
    while True:
        print("\n📋 Available actions:")
        print("1. Check pipeline status")
        print("2. Run pipeline for specific dealership")
        print("3. Run pipeline for all dealerships")
        print("4. View insights for dealership")
        print("5. Clear cache")
        print("6. Monitor active processing")
        print("7. Exit")
        
        choice = input("\nSelect an action (1-7): ").strip()
        
        if choice == "1":
            await check_pipeline_status()
        
        elif choice == "2":
            dealership_id = input("Enter dealership ID: ").strip()
            try:
                dealership_id = int(dealership_id)
                await run_pipeline_for_dealership(dealership_id)
            except ValueError:
                print("❌ Invalid dealership ID")
        
        elif choice == "3":
            confirm = input("Run pipeline for ALL dealerships? (y/N): ").strip().lower()
            if confirm == 'y':
                await run_pipeline_for_all()
        
        elif choice == "4":
            dealership_id = input("Enter dealership ID: ").strip()
            try:
                dealership_id = int(dealership_id)
                await view_insights(dealership_id)
            except ValueError:
                print("❌ Invalid dealership ID")
        
        elif choice == "5":
            dealership_id = input("Enter dealership ID (or press Enter for all): ").strip()
            if dealership_id:
                try:
                    dealership_id = int(dealership_id)
                    await clear_cache(dealership_id)
                except ValueError:
                    print("❌ Invalid dealership ID")
            else:
                await clear_cache()
        
        elif choice == "6":
            await monitor_processing()
        
        elif choice == "7":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    asyncio.run(main())
