#!/usr/bin/env python3
"""
Setup script for Lightspeed integration
This script helps configure the Lightspeed API integration
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import AsyncSessionLocal
from app.services.lightspeed_service import LightspeedService
from app.services.dealership_service import DealershipService
from app.models.dealership import Dealership
from app.models.lightspeed import LightspeedIntegration


async def check_lightspeed_config():
    """Check if Lightspeed configuration is properly set"""
    from app.core.config import settings
    
    print("🔍 Checking Lightspeed configuration...")
    
    required_settings = [
        ("LIGHTSPEED_CLIENT_ID", settings.lightspeed_client_id),
        ("LIGHTSPEED_CLIENT_SECRET", settings.lightspeed_client_secret),
        ("LIGHTSPEED_REDIRECT_URI", settings.lightspeed_redirect_uri),
        ("LIGHTSPEED_API_BASE_URL", settings.lightspeed_api_base_url),
    ]
    
    missing_settings = []
    for name, value in required_settings:
        if not value or value == "your-lightspeed-client-id":
            missing_settings.append(name)
        else:
            print(f"✅ {name}: {'*' * (len(value) - 4) + value[-4:]}")
    
    if missing_settings:
        print(f"\n❌ Missing required settings: {', '.join(missing_settings)}")
        print("\nPlease update your .env file with the following:")
        for setting in missing_settings:
            print(f"{setting}=your_actual_value")
        print("\nTo get these values:")
        print("1. Go to https://developers.retail.lightspeed.app/")
        print("2. Create a developer account")
        print("3. Create a new application")
        print("4. Copy the Client ID and Client Secret")
        return False
    
    print("✅ All Lightspeed settings configured!")
    return True


async def list_dealerships():
    """List all dealerships and their Lightspeed integration status"""
    async with AsyncSessionLocal() as db:
        dealership_service = DealershipService(db)
        lightspeed_service = LightspeedService(db)
        
        # Get all dealerships
        from sqlalchemy import select
        result = await db.execute(select(Dealership).where(Dealership.is_active == True))
        dealerships = result.scalars().all()
        
        if not dealerships:
            print("❌ No dealerships found. Create a dealership first.")
            return
        
        print(f"\n📋 Found {len(dealerships)} dealership(s):")
        print("-" * 80)
        
        for dealership in dealerships:
            integration = await lightspeed_service.get_integration_by_dealership(dealership.id)
            
            status = "❌ Not Connected"
            details = ""
            
            if integration:
                if integration.is_active:
                    # Test connection
                    try:
                        connection_test = await lightspeed_service.test_connection(integration)
                        if connection_test:
                            status = "✅ Connected & Working"
                            details = f"Shop: {integration.shop_name or integration.shop_id}"
                            if integration.last_successful_sync_at:
                                details += f", Last Sync: {integration.last_successful_sync_at.strftime('%Y-%m-%d %H:%M')}"
                        else:
                            status = "⚠️  Connected but Not Working"
                            details = f"Error: {integration.last_sync_error or 'Connection test failed'}"
                    except Exception as e:
                        status = "⚠️  Connected but Error"
                        details = f"Error: {str(e)}"
                else:
                    status = "⚠️  Connected but Inactive"
                    details = "Integration is disabled"
            
            print(f"ID: {dealership.id:3d} | {dealership.name:30s} | {status}")
            if details:
                print(f"     | {' ' * 30} | {details}")


async def test_integration(dealership_id: int):
    """Test Lightspeed integration for a specific dealership"""
    async with AsyncSessionLocal() as db:
        lightspeed_service = LightspeedService(db)
        
        integration = await lightspeed_service.get_integration_by_dealership(dealership_id)
        
        if not integration:
            print(f"❌ No Lightspeed integration found for dealership {dealership_id}")
            return
        
        print(f"🧪 Testing Lightspeed integration for dealership {dealership_id}...")
        
        try:
            # Test basic connection
            print("1. Testing API connection...")
            connection_test = await lightspeed_service.test_connection(integration)
            
            if not connection_test:
                print("❌ Connection test failed")
                return
            
            print("✅ Connection successful")
            
            # Test fetching products
            print("2. Testing product fetch...")
            products_response = await lightspeed_service.get_products(integration, page=1, page_size=5)
            product_count = len(products_response.get("data", []))
            print(f"✅ Fetched {product_count} products (sample)")
            
            # Test fetching sales
            print("3. Testing sales fetch...")
            sales_response = await lightspeed_service.get_sales(integration, page=1)
            sales_count = len(sales_response.get("data", []))
            print(f"✅ Fetched {sales_count} sales (sample)")
            
            print("\n🎉 All tests passed! Integration is working correctly.")
            
        except Exception as e:
            print(f"❌ Test failed: {str(e)}")


async def setup_webhook(dealership_id: int):
    """Setup webhook for real-time updates"""
    async with AsyncSessionLocal() as db:
        lightspeed_service = LightspeedService(db)
        
        integration = await lightspeed_service.get_integration_by_dealership(dealership_id)
        
        if not integration:
            print(f"❌ No Lightspeed integration found for dealership {dealership_id}")
            return
        
        print(f"🔗 Setting up webhook for dealership {dealership_id}...")
        
        # Webhook URL (you'll need to update this with your actual domain)
        webhook_url = "https://your-domain.com/api/v1/lightspeed/webhook"
        
        webhook_data = {
            "url": webhook_url,
            "active": True,
            "events": [
                "product.created",
                "product.updated", 
                "product.deleted",
                "sale.created",
                "sale.updated",
                "sale.deleted",
                "inventory.updated"
            ]
        }
        
        try:
            response = await lightspeed_service.make_api_request(
                integration,
                "webhooks",
                method="POST",
                data=webhook_data
            )
            
            webhook_id = response.get("data", {}).get("id")
            print(f"✅ Webhook created successfully! ID: {webhook_id}")
            print(f"📡 Webhook URL: {webhook_url}")
            print("📝 Events configured:")
            for event in webhook_data["events"]:
                print(f"   - {event}")
                
        except Exception as e:
            print(f"❌ Failed to create webhook: {str(e)}")


async def main():
    """Main setup function"""
    print("🚀 Lightspeed Integration Setup")
    print("=" * 50)
    
    # Check configuration
    config_ok = await check_lightspeed_config()
    if not config_ok:
        return
    
    while True:
        print("\n📋 Available actions:")
        print("1. List dealerships and integration status")
        print("2. Test integration for a dealership")
        print("3. Setup webhook for a dealership")
        print("4. Exit")
        
        choice = input("\nSelect an action (1-4): ").strip()
        
        if choice == "1":
            await list_dealerships()
        
        elif choice == "2":
            dealership_id = input("Enter dealership ID: ").strip()
            try:
                dealership_id = int(dealership_id)
                await test_integration(dealership_id)
            except ValueError:
                print("❌ Invalid dealership ID")
        
        elif choice == "3":
            dealership_id = input("Enter dealership ID: ").strip()
            try:
                dealership_id = int(dealership_id)
                await setup_webhook(dealership_id)
            except ValueError:
                print("❌ Invalid dealership ID")
        
        elif choice == "4":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    asyncio.run(main())
