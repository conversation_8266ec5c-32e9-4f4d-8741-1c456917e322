import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.data_pipeline_service import DataPipelineService, PipelineMetrics
from app.services.ai_service import AIService
from app.models.dealership import Dealership
from app.models.inventory import InventoryItem, Category
from app.models.sales import Sale, SaleItem, SaleStatus, PaymentMethod
from app.models.user import User


@pytest.fixture
async def test_dealership_with_data(db_session: AsyncSession):
    """Create a test dealership with sample data"""
    # Create dealership
    dealership = Dealership(
        name="Test Powersports",
        business_name="Test Powersports LLC",
        is_active=True
    )
    db_session.add(dealership)
    await db_session.flush()
    
    # Create category
    category = Category(
        name="ATVs",
        description="All-Terrain Vehicles"
    )
    db_session.add(category)
    await db_session.flush()
    
    # Create inventory items
    items = []
    for i in range(5):
        item = InventoryItem(
            dealership_id=dealership.id,
            category_id=category.id,
            sku=f"ATV-{i+1:03d}",
            name=f"Test ATV Model {i+1}",
            brand="TestBrand",
            retail_price=5000 + (i * 1000),
            cost_price=3000 + (i * 600),
            quantity_on_hand=10 - i,
            quantity_available=8 - i,
            reorder_level=3
        )
        items.append(item)
        db_session.add(item)
    
    await db_session.flush()
    
    # Create sales
    sales = []
    sale_items = []
    for i in range(3):
        sale = Sale(
            dealership_id=dealership.id,
            sale_number=f"SALE-{i+1:03d}",
            customer_name=f"Customer {i+1}",
            customer_email=f"customer{i+1}@example.com",
            subtotal=5000,
            tax_amount=400,
            total_amount=5400,
            status=SaleStatus.COMPLETED,
            payment_method=PaymentMethod.CREDIT_CARD,
            sale_date=datetime.utcnow() - timedelta(days=i)
        )
        sales.append(sale)
        db_session.add(sale)
        await db_session.flush()
        
        # Add sale items
        sale_item = SaleItem(
            sale_id=sale.id,
            inventory_item_id=items[i].id,
            sku=items[i].sku,
            name=items[i].name,
            quantity=1,
            unit_price=5000,
            total_price=5000,
            unit_cost=3000
        )
        sale_items.append(sale_item)
        db_session.add(sale_item)
    
    await db_session.commit()
    
    return {
        "dealership": dealership,
        "category": category,
        "inventory_items": items,
        "sales": sales,
        "sale_items": sale_items
    }


class TestDataPipelineService:
    """Test data pipeline service functionality"""
    
    @pytest.mark.asyncio
    async def test_extract_dealership_data(self, db_session: AsyncSession, test_dealership_with_data):
        """Test data extraction"""
        pipeline_service = DataPipelineService(db_session)
        dealership = test_dealership_with_data["dealership"]
        
        data = await pipeline_service._extract_dealership_data(dealership.id)
        
        assert data["dealership"].id == dealership.id
        assert len(data["inventory_items"]) == 5
        assert len(data["sales"]) == 3
        assert len(data["sale_items"]) == 3
        assert "extracted_at" in data
    
    @pytest.mark.asyncio
    async def test_inventory_to_dataframe(self, db_session: AsyncSession, test_dealership_with_data):
        """Test inventory data conversion to DataFrame"""
        pipeline_service = DataPipelineService(db_session)
        inventory_items = test_dealership_with_data["inventory_items"]
        
        df = pipeline_service._inventory_to_dataframe(inventory_items)
        
        assert len(df) == 5
        assert "sku" in df.columns
        assert "name" in df.columns
        assert "retail_price" in df.columns
        assert "margin" in df.columns
        assert df["brand"].iloc[0] == "TestBrand"
    
    @pytest.mark.asyncio
    async def test_sales_to_dataframe(self, db_session: AsyncSession, test_dealership_with_data):
        """Test sales data conversion to DataFrame"""
        pipeline_service = DataPipelineService(db_session)
        sales = test_dealership_with_data["sales"]
        sale_items = test_dealership_with_data["sale_items"]
        
        df = pipeline_service._sales_to_dataframe(sales, sale_items)
        
        assert len(df) == 3
        assert "sale_date" in df.columns
        assert "total_amount" in df.columns
        assert "profit" in df.columns
        assert df["total_amount"].iloc[0] == 5400
    
    @pytest.mark.asyncio
    async def test_calculate_inventory_insights(self, db_session: AsyncSession, test_dealership_with_data):
        """Test inventory insights calculation"""
        pipeline_service = DataPipelineService(db_session)
        inventory_items = test_dealership_with_data["inventory_items"]
        
        df = pipeline_service._inventory_to_dataframe(inventory_items)
        insights = await pipeline_service._calculate_inventory_insights(df)
        
        assert "total_items" in insights
        assert "total_value" in insights
        assert "avg_margin" in insights
        assert "top_brands" in insights
        assert "category_distribution" in insights
        assert insights["total_items"] == 5
        assert "TestBrand" in insights["top_brands"]
    
    @pytest.mark.asyncio
    async def test_calculate_sales_insights(self, db_session: AsyncSession, test_dealership_with_data):
        """Test sales insights calculation"""
        pipeline_service = DataPipelineService(db_session)
        sales = test_dealership_with_data["sales"]
        sale_items = test_dealership_with_data["sale_items"]
        
        df = pipeline_service._sales_to_dataframe(sales, sale_items)
        insights = await pipeline_service._calculate_sales_insights(df)
        
        assert "total_sales" in insights
        assert "total_revenue" in insights
        assert "avg_sale_amount" in insights
        assert "total_profit" in insights
        assert "sales_by_day" in insights
        assert insights["total_sales"] == 3
        assert insights["total_revenue"] == 16200  # 3 sales * 5400
    
    @pytest.mark.asyncio
    async def test_calculate_inventory_turnover(self, db_session: AsyncSession, test_dealership_with_data):
        """Test inventory turnover calculation"""
        pipeline_service = DataPipelineService(db_session)
        data = {
            "inventory_items": test_dealership_with_data["inventory_items"],
            "sale_items": test_dealership_with_data["sale_items"]
        }
        
        turnover = await pipeline_service._calculate_inventory_turnover(data)
        
        assert turnover is not None
        assert "top_performers" in turnover
        assert "slow_movers" in turnover
        assert "avg_turnover_rate" in turnover
    
    @pytest.mark.asyncio
    async def test_analyze_seasonal_trends(self, db_session: AsyncSession, test_dealership_with_data):
        """Test seasonal trends analysis"""
        pipeline_service = DataPipelineService(db_session)
        data = {"sales": test_dealership_with_data["sales"]}
        
        trends = await pipeline_service._analyze_seasonal_trends(data)
        
        assert trends is not None
        assert "monthly_breakdown" in trends
        assert "peak_season" in trends
        assert "low_season" in trends
    
    @pytest.mark.asyncio
    async def test_analyze_customer_patterns(self, db_session: AsyncSession, test_dealership_with_data):
        """Test customer behavior analysis"""
        pipeline_service = DataPipelineService(db_session)
        data = {"sales": test_dealership_with_data["sales"]}
        
        patterns = await pipeline_service._analyze_customer_patterns(data)
        
        assert patterns is not None
        assert "total_customers" in patterns
        assert "repeat_customers" in patterns
        assert "avg_purchases_per_customer" in patterns
        assert patterns["total_customers"] == 3
    
    @pytest.mark.asyncio
    async def test_analyze_profitability(self, db_session: AsyncSession, test_dealership_with_data):
        """Test profitability analysis"""
        pipeline_service = DataPipelineService(db_session)
        data = {"sale_items": test_dealership_with_data["sale_items"]}
        
        profitability = await pipeline_service._analyze_profitability(data)
        
        assert profitability is not None
        assert "overall_profit_margin" in profitability
        assert "total_profit" in profitability
        assert "most_profitable_products" in profitability
        assert profitability["total_profit"] == 6000  # 3 items * 2000 profit each
    
    @pytest.mark.asyncio
    @patch('app.services.data_pipeline_service.redis_client')
    async def test_update_cache(self, mock_redis, db_session: AsyncSession, test_dealership_with_data):
        """Test cache update functionality"""
        pipeline_service = DataPipelineService(db_session)
        dealership = test_dealership_with_data["dealership"]
        
        data = {
            "inventory_items": test_dealership_with_data["inventory_items"],
            "sales": test_dealership_with_data["sales"]
        }
        insights = [{"type": "test", "data": {"test": "value"}}]
        
        mock_redis.set = AsyncMock()
        
        await pipeline_service._update_cache(dealership.id, data, insights)
        
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        assert call_args[0][0] == f"dealership_insights:{dealership.id}"
        assert call_args[1]["expire"] == 3600
    
    @pytest.mark.asyncio
    @patch('app.services.ai_service.AIService')
    async def test_process_inventory_data_with_ai(self, mock_ai_service, db_session: AsyncSession, test_dealership_with_data):
        """Test inventory processing with AI insights"""
        # Mock AI service
        mock_ai_instance = AsyncMock()
        mock_ai_instance.generate_inventory_insights.return_value = {
            "stock_status": "optimal",
            "reorder_recommendation": "No action needed",
            "confidence": "high"
        }
        mock_ai_service.return_value = mock_ai_instance
        
        pipeline_service = DataPipelineService(db_session)
        dealership = test_dealership_with_data["dealership"]
        
        data = await pipeline_service._extract_dealership_data(dealership.id)
        metrics = await pipeline_service._process_inventory_data(dealership.id, data)
        
        assert metrics.records_processed == 5
        assert metrics.insights_generated == 5
        assert mock_ai_instance.generate_inventory_insights.call_count == 5
    
    @pytest.mark.asyncio
    @patch('app.services.ai_service.AIService')
    @patch('app.services.data_pipeline_service.redis_client')
    async def test_full_pipeline_execution(self, mock_redis, mock_ai_service, db_session: AsyncSession, test_dealership_with_data):
        """Test complete pipeline execution"""
        # Mock AI service
        mock_ai_instance = AsyncMock()
        mock_ai_instance.generate_inventory_insights.return_value = {"test": "insight"}
        mock_ai_instance.generate_sales_insights.return_value = {"test": "sales_insight"}
        mock_ai_service.return_value = mock_ai_instance
        
        # Mock Redis
        mock_redis.set = AsyncMock()
        
        pipeline_service = DataPipelineService(db_session)
        dealership = test_dealership_with_data["dealership"]
        
        metrics = await pipeline_service.process_dealership_data(dealership.id)
        
        assert isinstance(metrics, PipelineMetrics)
        assert metrics.records_processed > 0
        assert metrics.insights_generated > 0
        assert metrics.execution_time > 0
        assert metrics.errors == 0
        
        # Verify AI service was called
        assert mock_ai_instance.generate_inventory_insights.call_count == 5
        assert mock_ai_instance.generate_sales_insights.call_count == 3
        
        # Verify cache was updated
        mock_redis.set.assert_called_once()


class TestAIService:
    """Test AI service functionality"""
    
    @pytest.mark.asyncio
    @patch('openai.ChatCompletion.create')
    async def test_generate_inventory_insights(self, mock_openai):
        """Test AI inventory insights generation"""
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '{"stock_status": "low", "reorder_recommendation": "Order 10 units"}'
        mock_openai.return_value = mock_response
        
        ai_service = AIService()
        
        item_data = {
            "sku": "TEST-001",
            "name": "Test Item",
            "quantity_on_hand": 2,
            "reorder_level": 5,
            "retail_price": 100,
            "cost_price": 60
        }
        
        context = {"total_items": 100, "avg_margin": 35.0}
        
        with patch('asyncio.to_thread', return_value=mock_response):
            insights = await ai_service.generate_inventory_insights(item_data, context)
        
        assert "stock_status" in insights
        assert "reorder_recommendation" in insights
        assert "generated_at" in insights
        assert insights["sku"] == "TEST-001"
    
    @pytest.mark.asyncio
    @patch('openai.ChatCompletion.create')
    async def test_generate_sales_insights(self, mock_openai):
        """Test AI sales insights generation"""
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = '{"performance_assessment": "above average", "customer_insight": "loyal customer"}'
        mock_openai.return_value = mock_response
        
        ai_service = AIService()
        
        sale_data = {
            "sale_id": 1,
            "total_amount": 5000,
            "customer_name": "John Doe",
            "items": [{"name": "ATV", "total_price": 5000}]
        }
        
        context = {"avg_sale_amount": 3000, "total_sales": 100}
        
        with patch('asyncio.to_thread', return_value=mock_response):
            insights = await ai_service.generate_sales_insights(sale_data, context)
        
        assert "performance_assessment" in insights
        assert "customer_insight" in insights
        assert "generated_at" in insights
        assert insights["sale_id"] == 1
