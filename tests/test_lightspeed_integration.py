import pytest
from unittest.mock import AsyncMock, patch
from httpx import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.lightspeed_service import LightspeedService, LightspeedAPIError
from app.models.lightspeed import LightspeedIntegration
from app.models.dealership import Dealership, DealershipUser, UserRole
from app.models.user import User


@pytest.fixture
async def test_user(db_session: AsyncSession):
    """Create a test user"""
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        first_name="Test",
        last_name="User",
        is_active=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
async def test_dealership(db_session: AsyncSession, test_user: User):
    """Create a test dealership"""
    dealership = Dealership(
        name="Test Dealership",
        business_name="Test Business",
        is_active=True
    )
    db_session.add(dealership)
    await db_session.flush()
    
    # Add user to dealership
    dealership_user = DealershipUser(
        dealership_id=dealership.id,
        user_id=test_user.id,
        role=UserRole.OWNER,
        is_active=True
    )
    db_session.add(dealership_user)
    await db_session.commit()
    await db_session.refresh(dealership)
    return dealership


@pytest.fixture
async def lightspeed_service(db_session: AsyncSession):
    """Create LightspeedService instance"""
    return LightspeedService(db_session)


class TestLightspeedService:
    """Test Lightspeed service functionality"""
    
    def test_get_authorization_url(self, lightspeed_service: LightspeedService):
        """Test OAuth authorization URL generation"""
        state = "test_state_123"
        url = lightspeed_service.get_authorization_url(state)
        
        assert "secure.retail.lightspeed.app/connect" in url
        assert f"state={state}" in url
        assert "response_type=code" in url
    
    @pytest.mark.asyncio
    async def test_exchange_code_for_token_success(self, lightspeed_service: LightspeedService):
        """Test successful token exchange"""
        mock_response = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
            "domain_prefix": "test_domain"
        }
        
        with patch("httpx.AsyncClient.post") as mock_post:
            mock_post.return_value.json.return_value = mock_response
            mock_post.return_value.raise_for_status.return_value = None
            
            result = await lightspeed_service.exchange_code_for_token("test_code", "test_domain")
            
            assert result["access_token"] == "test_access_token"
            assert result["refresh_token"] == "test_refresh_token"
    
    @pytest.mark.asyncio
    async def test_exchange_code_for_token_failure(self, lightspeed_service: LightspeedService):
        """Test failed token exchange"""
        with patch("httpx.AsyncClient.post") as mock_post:
            mock_post.return_value.raise_for_status.side_effect = Exception("HTTP Error")
            
            with pytest.raises(LightspeedAPIError):
                await lightspeed_service.exchange_code_for_token("invalid_code", "test_domain")
    
    @pytest.mark.asyncio
    async def test_create_integration(
        self, 
        lightspeed_service: LightspeedService, 
        test_dealership: Dealership
    ):
        """Test creating Lightspeed integration"""
        auth_data = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
            "domain_prefix": "test_domain"
        }
        
        integration = await lightspeed_service.create_integration(test_dealership.id, auth_data)
        
        assert integration.dealership_id == test_dealership.id
        assert integration.access_token == "test_access_token"
        assert integration.refresh_token == "test_refresh_token"
        assert integration.shop_id == "test_domain"
        assert integration.is_active is True
    
    @pytest.mark.asyncio
    async def test_make_api_request_success(
        self, 
        lightspeed_service: LightspeedService,
        test_dealership: Dealership
    ):
        """Test successful API request"""
        # Create integration
        auth_data = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
            "domain_prefix": "test_domain"
        }
        integration = await lightspeed_service.create_integration(test_dealership.id, auth_data)
        
        mock_response = {"data": [{"id": "1", "name": "Test Product"}]}
        
        with patch("httpx.AsyncClient.get") as mock_get:
            mock_get.return_value.json.return_value = mock_response
            mock_get.return_value.raise_for_status.return_value = None
            mock_get.return_value.status_code = 200
            
            result = await lightspeed_service.make_api_request(integration, "products")
            
            assert result["data"][0]["name"] == "Test Product"
    
    @pytest.mark.asyncio
    async def test_make_api_request_rate_limited(
        self, 
        lightspeed_service: LightspeedService,
        test_dealership: Dealership
    ):
        """Test API request with rate limiting"""
        # Create integration
        auth_data = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
            "domain_prefix": "test_domain"
        }
        integration = await lightspeed_service.create_integration(test_dealership.id, auth_data)
        
        with patch("httpx.AsyncClient.get") as mock_get:
            # First call returns 429, second call succeeds
            mock_get.return_value.status_code = 429
            mock_get.return_value.headers = {"Retry-After": "1"}
            
            with patch("asyncio.sleep") as mock_sleep:
                with pytest.raises(LightspeedAPIError):
                    await lightspeed_service.make_api_request(integration, "products")


class TestLightspeedAPI:
    """Test Lightspeed API endpoints"""
    
    @pytest.mark.asyncio
    async def test_get_auth_url_endpoint(self, client: AsyncClient, test_user: User, test_dealership: Dealership):
        """Test getting OAuth authorization URL"""
        # Mock authentication
        with patch("app.api.v1.endpoints.auth.get_current_user", return_value=test_user):
            response = await client.get(f"/api/v1/lightspeed/auth-url?dealership_id={test_dealership.id}")
            
            assert response.status_code == 200
            data = response.json()
            assert "auth_url" in data
            assert "state" in data
            assert "secure.retail.lightspeed.app" in data["auth_url"]
    
    @pytest.mark.asyncio
    async def test_oauth_callback_success(self, client: AsyncClient, test_dealership: Dealership):
        """Test successful OAuth callback"""
        # Mock Redis state data
        state_data = {
            "dealership_id": test_dealership.id,
            "user_id": 1
        }
        
        mock_token_response = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
            "domain_prefix": "test_domain"
        }
        
        with patch("app.core.redis.redis_client.get", return_value=state_data):
            with patch("app.core.redis.redis_client.delete", return_value=True):
                with patch("httpx.AsyncClient.post") as mock_post:
                    mock_post.return_value.json.return_value = mock_token_response
                    mock_post.return_value.raise_for_status.return_value = None
                    
                    with patch("app.services.lightspeed_service.LightspeedService.test_connection", return_value=True):
                        response = await client.post("/api/v1/lightspeed/callback", json={
                            "code": "test_code",
                            "state": "test_state",
                            "domain_prefix": "test_domain"
                        })
                        
                        assert response.status_code == 200
                        data = response.json()
                        assert data["message"] == "Lightspeed integration successful"
    
    @pytest.mark.asyncio
    async def test_oauth_callback_invalid_state(self, client: AsyncClient):
        """Test OAuth callback with invalid state"""
        with patch("app.core.redis.redis_client.get", return_value=None):
            response = await client.post("/api/v1/lightspeed/callback", json={
                "code": "test_code",
                "state": "invalid_state",
                "domain_prefix": "test_domain"
            })
            
            assert response.status_code == 400
            assert "Invalid or expired state parameter" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_get_connection_status(self, client: AsyncClient, test_user: User, test_dealership: Dealership):
        """Test getting connection status"""
        with patch("app.api.v1.endpoints.auth.get_current_user", return_value=test_user):
            response = await client.get(f"/api/v1/lightspeed/status/{test_dealership.id}")
            
            assert response.status_code == 200
            data = response.json()
            assert "is_connected" in data
            assert data["is_connected"] is False  # No integration exists yet
    
    @pytest.mark.asyncio
    async def test_trigger_sync(self, client: AsyncClient, test_user: User, test_dealership: Dealership):
        """Test triggering manual sync"""
        # Create integration first
        integration = LightspeedIntegration(
            dealership_id=test_dealership.id,
            access_token="test_token",
            refresh_token="test_refresh",
            shop_id="test_domain",
            is_active=True
        )
        
        with patch("app.api.v1.endpoints.auth.get_current_user", return_value=test_user):
            with patch("app.services.lightspeed_service.LightspeedService.get_integration_by_dealership", return_value=integration):
                with patch("app.tasks.lightspeed_sync.sync_dealership_data.delay") as mock_delay:
                    mock_delay.return_value.id = "test_task_id"
                    
                    response = await client.post(
                        f"/api/v1/lightspeed/sync/{test_dealership.id}",
                        json={"sync_type": "manual"}
                    )
                    
                    assert response.status_code == 200
                    data = response.json()
                    assert data["status"] == "queued"
                    assert data["task_id"] == "test_task_id"
