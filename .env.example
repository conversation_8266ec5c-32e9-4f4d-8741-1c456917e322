# Database Configuration
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/dealdash_db
DATABASE_TEST_URL=postgresql+asyncpg://user:password@localhost:5432/dealdash_test_db

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Lightspeed API Configuration
LIGHTSPEED_CLIENT_ID=your-lightspeed-client-id
LIGHTSPEED_CLIENT_SECRET=your-lightspeed-client-secret
LIGHTSPEED_REDIRECT_URI=http://localhost:8000/auth/lightspeed/callback
LIGHTSPEED_API_BASE_URL=https://api.lightspeedretail.com

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Application Configuration
APP_NAME=DealDash Platform
APP_VERSION=1.0.0
DEBUG=True
ENVIRONMENT=development

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100

# File Upload
MAX_FILE_SIZE_MB=10
UPLOAD_DIR=./uploads
